defmodule MqttableWeb.Live.Components.ArrayDataExample do
  @moduledoc """
  Provides example templates for JSON arrays with various data types,
  including examples using Solid's for loop syntax.
  """

  @doc """
  Returns a template example for a JSON array using for loop.
  """
  def for_loop_example do
    """
    {
      "device_id": "{{ device_id }}",
      "timestamp": "{{ iso8601 }}",
      "readings": [
        {% for i in (1..5) %}
          {
            "id": {{ i }},
            "temperature": {{ temperature }},
            "humidity": {{ humidity }}
          }{% unless forloop.last %},{% endunless %}
        {% endfor %}
      ]
    }
    """
  end

  @doc """
  Returns a template example for a JSON array using for loop with an array.
  """
  def for_loop_array_example do
    """
    {
      "device_id": "{{ device_id }}",
      "timestamp": "{{ iso8601 }}",
      "sensors": [
        {% assign sensor_types = "temperature,humidity,pressure,battery" | split: "," %}
        {% for sensor in sensor_types %}
          {
            "type": "{{ sensor }}",
            "value": {% case sensor %}
              {% when "temperature" %}{{ temperature }}
              {% when "humidity" %}{{ humidity }}
              {% when "pressure" %}{{ pressure }}
              {% when "battery" %}{{ battery_level }}
              {% else %}0
            {% endcase %},
            "unit": "{% case sensor %}
              {% when "temperature" %}C
              {% when "humidity" %}%
              {% when "pressure" %}hPa
              {% when "battery" %}%
              {% else %}unknown
            {% endcase %}"
          }{% unless forloop.last %},{% endunless %}
        {% endfor %}
      ]
    }
    """
  end

  @doc """
  Returns a template example for a nested for loop.
  """
  def nested_for_loop_example do
    """
    {
      "farm_id": "{{ uuid }}",
      "timestamp": "{{ iso8601 }}",
      "zones": [
        {% for zone_index in (1..3) %}
          {
            "zone_id": "zone_{{ zone_index }}",
            "name": "Zone {{ zone_index }}",
            "sensors": [
              {% for sensor_index in (1..2) %}
                {
                  "id": "sensor_{{ zone_index }}_{{ sensor_index }}",
                  "temperature": {{ temperature }},
                  "humidity": {{ humidity }}
                }{% unless forloop.last %},{% endunless %}
              {% endfor %}
            ]
          }{% unless forloop.last %},{% endunless %}
        {% endfor %}
      ]
    }
    """
  end

  @doc """
  Returns a template example using for loop with conditional logic.
  """
  def conditional_for_loop_example do
    """
    {
      "device_id": "{{ device_id }}",
      "timestamp": "{{ iso8601 }}",
      "data": [
        {% for i in (1..10) %}
          {% if i > 5 %}
            {
              "id": {{ i }},
              "value": {{ random_int }},
              "type": "high"
            }{% unless forloop.last %},{% endunless %}
          {% else %}
            {
              "id": {{ i }},
              "value": {{ random_int }},
              "type": "low"
            }{% unless forloop.last %},{% endunless %}
          {% endif %}
        {% endfor %}
      ]
    }
    """
  end
end
