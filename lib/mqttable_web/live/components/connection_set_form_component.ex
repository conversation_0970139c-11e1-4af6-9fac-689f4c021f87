defmodule MqttableWeb.ConnectionSetFormComponent do
  @moduledoc """
  Shared functionality for connection set form components.
  This module contains common functions and components used by both
  NewConnectionSetModalComponent and EditConnectionSetModalComponent.
  """
  use MqttableWeb, :html
  alias Mqttable.Uploads.CertificateHandler

  # Public function to get upload options
  def upload_options do
    CertificateHandler.upload_options()
  end

  # Helper function to identify duplicate variable names and assign colors
  def get_duplicate_colors(variables) do
    # Count occurrences of each name
    name_counts =
      variables
      |> Enum.filter(fn var -> var.name != "" end)
      |> Enum.group_by(fn var -> var.name end)
      |> Enum.map(fn {name, vars} -> {name, length(vars)} end)
      |> Enum.into(%{})

    # Assign colors to duplicate names
    duplicate_colors =
      name_counts
      |> Enum.filter(fn {_name, count} -> count > 1 end)
      |> Enum.with_index()
      |> Enum.map(fn {{name, _count}, index} ->
        color = get_duplicate_color(index)
        {name, color}
      end)
      |> Enum.into(%{})

    duplicate_colors
  end

  # Helper function to get a color for a duplicate based on index
  def get_duplicate_color(index) do
    colors = ["text-error", "text-warning", "text-info", "text-success", "text-secondary"]
    Enum.at(colors, rem(index, length(colors)))
  end

  # Helper function to add an empty variable row
  def add_empty_variable_row(set) do
    # Ensure variables exist
    variables = Map.get(set, :variables, [])

    # If there are already variables, don't add an empty row
    if length(variables) > 0 do
      set
    else
      # If no variables exist, add an empty row
      empty_var = %{name: "", value: "", enabled: true}
      updated_vars = [empty_var]
      Map.put(set, :variables, updated_vars)
    end
  end

  # Common event handler for adding a new variable row
  def handle_add_variable_row(_params, socket) do
    edit_connection_set = socket.assigns.edit_connection_set
    variables = Map.get(edit_connection_set, :variables, [])

    # Add a new empty row
    new_var = %{name: "", value: "", enabled: true}
    updated_vars = variables ++ [new_var]

    # Update the connection set with the new variables list
    updated_set = Map.put(edit_connection_set, :variables, updated_vars)

    {:noreply, assign(socket, :edit_connection_set, updated_set)}
  end

  # Common event handler for deleting variables
  def handle_delete_variable(params, socket) do
    %{"index" => index_str} = params
    index = String.to_integer(index_str)
    edit_connection_set = socket.assigns.edit_connection_set
    variables = Map.get(edit_connection_set, :variables, [])

    # Remove the variable at the specified index
    updated_vars =
      variables
      |> Enum.with_index()
      |> Enum.reject(fn {_var, i} -> i == index end)
      |> Enum.map(fn {var, _i} -> var end)

    # Add a timestamp to force re-render of the variables table
    updated_set =
      edit_connection_set
      |> Map.put(:variables, updated_vars)
      |> Map.put(:_clear_timestamp, DateTime.utc_now() |> DateTime.to_string())

    # Update the socket with the new connection set
    {:noreply, assign(socket, :edit_connection_set, updated_set)}
  end

  # Common event handler for clearing variables
  def handle_clear_variables(_params, socket) do
    edit_connection_set = socket.assigns.edit_connection_set

    # Create a new empty variable for the empty row
    empty_var = %{name: "", value: "", enabled: true}

    # Update the set with an empty variables list and a timestamp
    updated_set =
      edit_connection_set
      |> Map.put(:variables, [empty_var])
      |> Map.put(:_clear_timestamp, DateTime.utc_now() |> DateTime.to_string())

    {:noreply, assign(socket, :edit_connection_set, updated_set)}
  end

  # Common event handler for updating variable fields in real-time
  def handle_update_variable(params, socket) do
    edit_connection_set = socket.assigns.edit_connection_set
    variables = Map.get(edit_connection_set, :variables, [])

    # Extract the target information from params
    target = params["_target"]

    if target && is_list(target) && length(target) >= 3 && hd(target) == "variable" do
      # Extract index and field from target
      [_, index_str, field] = target
      index = String.to_integer(index_str)

      # Get the value from the variable params
      value =
        case params do
          %{"variable" => var_params} when is_map(var_params) ->
            case var_params do
              %{^index_str => field_params} when is_map(field_params) ->
                Map.get(field_params, field, "")

              _ ->
                ""
            end

          _ ->
            ""
        end

      # Update the variable at the specified index
      updated_vars =
        variables
        |> Enum.with_index()
        |> Enum.map(fn {var, i} ->
          if i == index do
            # Update the specified field
            Map.put(var, String.to_atom(field), value)
          else
            var
          end
        end)

      # Update the connection set with the updated variables
      # But preserve all other fields from the original connection set
      updated_set = Map.put(edit_connection_set, :variables, updated_vars)

      {:noreply, assign(socket, :edit_connection_set, updated_set)}
    else
      # If we can't extract the target information, just return the socket unchanged
      {:noreply, socket}
    end
  end

  # Common event handler for updating form fields in real-time
  def handle_update_form_field(params, socket) do
    edit_connection_set = socket.assigns.edit_connection_set

    # Extract the connection_set params if they exist
    updated_set =
      case params do
        %{"connection_set" => connection_set_params} when is_map(connection_set_params) ->
          # Update each field in the connection set
          Enum.reduce(connection_set_params, edit_connection_set, fn {key, value}, acc ->
            # Convert key to atom and update the connection set
            Map.put(acc, String.to_atom(key), value)
          end)

        _ ->
          # If no connection_set params, return the original
          edit_connection_set
      end

    # Return the updated socket
    {:noreply, assign(socket, :edit_connection_set, updated_set)}
  end

  # Common event handler for name validation
  def handle_validate_name(params, socket) do
    alias MqttableWeb.Utils.ConnectionValidation

    # Get the current connection set being edited
    edit_connection_set = socket.assigns.edit_connection_set

    # Get the name from the params
    name =
      cond do
        # Handle direct value format
        Map.has_key?(params, "value") ->
          params["value"]

        # Handle form submission format
        Map.has_key?(params, "connection_set") &&
            Map.has_key?(params["connection_set"], "name") ->
          params["connection_set"]["name"]

        # Default case - use the current name
        true ->
          Map.get(edit_connection_set, :name, "")
      end

    # Get the old name (for edit mode)
    old_name = Map.get(edit_connection_set, :name, "")

    # Get all existing connection sets from the parent LiveView
    all_connection_sets = socket.assigns[:connection_sets] || []

    # Use the new validation function
    case ConnectionValidation.validate_broker_name_uniqueness(name, all_connection_sets, old_name) do
      {:ok, _} ->
        # Clear any existing name error
        updated_set = Map.put(edit_connection_set, :name_error, nil)
        {:noreply, assign(socket, :edit_connection_set, updated_set)}

      {:error, error_message} ->
        # Set the name error
        updated_set = Map.put(edit_connection_set, :name_error, error_message)
        {:noreply, assign(socket, :edit_connection_set, updated_set)}
    end
  end

  # Common event handler for protocol change
  def handle_protocol_changed(params, socket) do
    # Extract protocol from params
    protocol =
      cond do
        # Handle direct value format
        Map.has_key?(params, "value") ->
          params["value"]

        # Handle form submission format
        Map.has_key?(params, "connection_set") &&
            Map.has_key?(params["connection_set"], "protocol") ->
          params["connection_set"]["protocol"]

        # Default case
        true ->
          socket.assigns.edit_connection_set.protocol
      end

    # Update the edit_connection_set with the new protocol
    updated_set = Map.put(socket.assigns.edit_connection_set, :protocol, protocol)

    # Update default port and WebSocket path based on protocol
    updated_set =
      case protocol do
        "mqtt" ->
          Map.put(updated_set, :port, "1883")

        "mqtts" ->
          Map.put(updated_set, :port, "8883")

        "ws" ->
          updated_set
          |> Map.put(:port, "8083")
          |> Map.put(:ws_path, Map.get(updated_set, :ws_path, "/mqtt"))

        "wss" ->
          updated_set
          |> Map.put(:port, "8084")
          |> Map.put(:ws_path, Map.get(updated_set, :ws_path, "/mqtt"))

        "quic" ->
          Map.put(updated_set, :port, "14567")

        _ ->
          updated_set
      end

    # Return the updated socket
    {:noreply, assign(socket, :edit_connection_set, updated_set)}
  end

  # Shared UI component for protocol/host/port input
  def connection_set_form_fields(assigns) do
    ~H"""
    <div class="form-control mb-4 w-full flex items-center">
      <input
        type="text"
        name="connection_set[name]"
        value={Map.get(@edit_connection_set, :name, "")}
        class={[
          "input input-bordered flex-grow text-center",
          if(Map.get(@edit_connection_set, :name_error), do: "input-error", else: "")
        ]}
        placeholder="What is this broker for—production or testing?"
        required
        id="connection-set-name"
        phx-blur="validate_name"
        phx-change="update_form_field"
        phx-target={@myself}
      />
      <%= if Map.get(@edit_connection_set, :name_error) do %>
        <label class="label">
          <span class="label-text-alt text-error">{Map.get(@edit_connection_set, :name_error)}</span>
        </label>
      <% end %>
    </div>

    <div class="form-control">
      <div class="join w-full">
        <select
          class="select select-bordered join-item w-24"
          name="connection_set[protocol]"
          id="connection-set-protocol"
          phx-change="protocol_changed"
          phx-target={@myself}
          phx-hook="ProtocolSelector"
        >
          <option value="mqtt" selected={Map.get(@edit_connection_set, :protocol) == "mqtt"}>
            mqtt
          </option>
          <option value="mqtts" selected={Map.get(@edit_connection_set, :protocol) == "mqtts"}>
            mqtts
          </option>
          <option value="ws" selected={Map.get(@edit_connection_set, :protocol) == "ws"}>
            ws
          </option>
          <option value="wss" selected={Map.get(@edit_connection_set, :protocol) == "wss"}>
            wss
          </option>
          <option value="quic" selected={Map.get(@edit_connection_set, :protocol) == "quic"}>
            quic
          </option>
        </select>

        <div class="flex-grow">
          <input
            type="text"
            name="connection_set[host]"
            value={Map.get(@edit_connection_set, :host, "")}
            class="input input-bordered join-item w-full"
            placeholder="Host (URL or IP)"
            required
            id="connection-set-host"
            phx-change="update_form_field"
            phx-target={@myself}
          />
        </div>

        <div class="w-20">
          <input
            type="number"
            name="connection_set[port]"
            value={Map.get(@edit_connection_set, :port, "1883")}
            class="input input-bordered join-item w-full"
            placeholder="Port"
            required
            phx-change="update_form_field"
            phx-target={@myself}
          />
        </div>
      </div>
    </div>

    <!-- WebSocket Path Section -->
    <div
      class="mt-4"
      id="websocket-path-section"
      style={
        if Map.get(@edit_connection_set, :protocol) in ["ws", "wss"],
          do: "",
          else: "display: none;"
      }
    >
      <div class="form-control">
        <label class="label">
          <span class="label-text">WebSocket Path</span>
        </label>
        <input
          type="text"
          name="connection_set[ws_path]"
          value={Map.get(@edit_connection_set, :ws_path, "/mqtt")}
          class="input input-bordered w-full"
          placeholder="/mqtt"
          phx-change="update_form_field"
          phx-target={@myself}
        />
        <label class="label">
          <span class="label-text-alt">The path component of the WebSocket URL (e.g., /mqtt)</span>
        </label>
      </div>
    </div>

    <div
      class="mt-4"
      id="ssl-section-container"
      style={
        if Map.get(@edit_connection_set, :protocol) in ["mqtts", "wss", "quic"],
          do: "",
          else: "display: none;"
      }
    >
      <.ssl_section
        edit_connection_set={@edit_connection_set}
        uploads={assigns[:uploads]}
        upload_target={@myself}
      />
    </div>
    """
  end

  # Helper function to render the SSL section for connection sets
  def ssl_section(assigns) do
    # Ensure myself is available in assigns or use a default
    assigns = assign_new(assigns, :myself, fn -> assigns[:upload_target] end)

    ~H"""
    <div class="bg-base-100 p-4 rounded-lg border border-gray-200">
      <div class="flex items-center justify-between mb-3">
        <div class="flex items-center gap-2">
          <img src="/images/ssl.svg" alt="SSL" class="w-5 h-5 inline-block" />
          <span class="text-gray-700 font-medium">SSL Secure</span>
          <input
            type="checkbox"
            name="connection_set[ssl_enabled]"
            checked={Map.get(@edit_connection_set, :ssl_enabled, false)}
            class="toggle toggle-primary"
          />
        </div>
        <button type="button" class="btn btn-ghost btn-xs btn-circle">
          <.icon name="hero-information-circle" class="h-4 w-4 text-gray-500" />
        </button>
      </div>

      <div class="grid grid-cols-1 gap-4">
        <div class="form-control">
          <div class="flex items-center gap-2">
            <span class="label-text text-gray-500 w-10">
              ALPN
            </span>
            <input
              type="text"
              name="connection_set[alpn]"
              value={Map.get(@edit_connection_set, :alpn, "")}
              class="input input-bordered flex-grow"
              placeholder="Application-Layer Protocol Negotiation"
              phx-change="update_form_field"
              phx-target={@myself}
            />
          </div>
        </div>

        <div class="form-control">
          <label class="label">
            <span class="label-text">Certificate</span>
          </label>
          <div class="flex gap-4">
            <label class="flex items-center gap-2 cursor-pointer">
              <input
                type="radio"
                name="connection_set[certificate_type]"
                value="ca_signed"
                checked={Map.get(@edit_connection_set, :certificate_type, "ca_signed") == "ca_signed"}
                class="radio radio-primary certificate-type-radio"
                phx-change="update_form_field"
                phx-target={@myself}
                id="certificate-type-ca-signed"
                onchange="updateCertificateFilesVisibility()"
              />
              <span class="label-text">CA signed server certificate</span>
            </label>
            <label class="flex items-center gap-2 cursor-pointer">
              <input
                type="radio"
                name="connection_set[certificate_type]"
                value="self_signed"
                checked={
                  Map.get(@edit_connection_set, :certificate_type, "ca_signed") == "self_signed"
                }
                class="radio radio-primary certificate-type-radio"
                phx-change="update_form_field"
                phx-target={@myself}
                id="certificate-type-self-signed"
                onchange="updateCertificateFilesVisibility()"
              />
              <span class="label-text">CA or Self signed certificates</span>
            </label>
          </div>
        </div>

        <div
          id="certificate-files-section"
          style={
            if Map.get(@edit_connection_set, :certificate_type, "ca_signed") == "self_signed",
              do: "",
              else: "display: none;"
          }
        >
          <div class="form-control">
            <label class="label">
              <span class="label-text">CA File</span>
            </label>
            <div class="flex flex-col gap-2">
              <div class="flex gap-2 items-center">
                <input
                  type="text"
                  name="connection_set[ca_file]"
                  value={Map.get(@edit_connection_set, :ca_file, "")}
                  class="input input-bordered flex-grow"
                  placeholder="Path to CA file"
                />
                <%= if @uploads do %>
                  <div class="w-1/3">
                    <.live_file_input
                      upload={@uploads.ca_file}
                      class="file-input file-input-bordered w-full"
                      accept=".pem,.crt,.der"
                    />
                  </div>
                <% end %>
              </div>
              <%= if @uploads do %>
                <%= for entry <- @uploads.ca_file.entries do %>
                  <.file_upload_entry
                    entry={entry}
                    upload_name={:ca_file}
                    uploads={@uploads}
                    upload_target={@upload_target}
                  />
                <% end %>
              <% end %>
            </div>
          </div>

          <div class="form-control mt-4">
            <label class="label">
              <span class="label-text">Client Certificate File</span>
            </label>
            <div class="flex flex-col gap-2">
              <div class="flex gap-2 items-center">
                <input
                  type="text"
                  name="connection_set[client_cert_file]"
                  value={Map.get(@edit_connection_set, :client_cert_file, "")}
                  class="input input-bordered flex-grow"
                  placeholder="Path to client certificate file"
                />
                <%= if @uploads do %>
                  <div class="w-1/3">
                    <.live_file_input
                      upload={@uploads.client_cert_file}
                      class="file-input file-input-bordered w-full"
                      accept=".pem,.crt,.der"
                    />
                  </div>
                <% end %>
              </div>
              <%= if @uploads do %>
                <%= for entry <- @uploads.client_cert_file.entries do %>
                  <.file_upload_entry
                    entry={entry}
                    upload_name={:client_cert_file}
                    uploads={@uploads}
                    upload_target={@upload_target}
                  />
                <% end %>
              <% end %>
            </div>
          </div>

          <div class="form-control mt-4">
            <label class="label">
              <span class="label-text">Client Key File</span>
            </label>
            <div class="flex flex-col gap-2">
              <div class="flex gap-2 items-center">
                <input
                  type="text"
                  name="connection_set[client_key_file]"
                  value={Map.get(@edit_connection_set, :client_key_file, "")}
                  class="input input-bordered flex-grow"
                  placeholder="Path to client key file"
                />
                <%= if @uploads do %>
                  <div class="w-1/3">
                    <.live_file_input
                      upload={@uploads.client_key_file}
                      class="file-input file-input-bordered w-full"
                      accept=".pem,.key,.der"
                    />
                  </div>
                <% end %>
              </div>
              <%= if @uploads do %>
                <%= for entry <- @uploads.client_key_file.entries do %>
                  <.file_upload_entry
                    entry={entry}
                    upload_name={:client_key_file}
                    uploads={@uploads}
                    upload_target={@upload_target}
                  />
                <% end %>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  # Helper function to render file upload entry
  def file_upload_entry(assigns) do
    ~H"""
    <div class="flex items-center justify-between bg-base-200 p-2 rounded-lg">
      <div class="flex items-center gap-2">
        <.icon name="hero-document" class="h-4 w-4" />
        <span class="text-sm">{@entry.client_name}</span>
      </div>
      <div class="flex items-center gap-2">
        <progress value={@entry.progress} max="100" class="progress progress-primary w-24"></progress>
        <button
          type="button"
          phx-click="cancel-upload"
          phx-value-ref={@entry.ref}
          phx-value-upload={@upload_name}
          phx-target={@upload_target}
          class="btn btn-ghost btn-xs btn-circle"
        >
          <.icon name="hero-x-mark" class="h-4 w-4" />
        </button>
      </div>
    </div>
    <%= if @uploads do %>
      <%= for err <- upload_errors(@uploads[@upload_name], @entry) do %>
        <div class="alert alert-error mt-1 py-2 text-sm">
          <.icon name="hero-exclamation-circle" class="h-4 w-4" />
          <span>{error_to_string(err)}</span>
        </div>
      <% end %>
    <% end %>
    """
  end

  # Helper function to convert error to string
  def error_to_string(:too_large), do: "File is too large"
  def error_to_string(:too_many_files), do: "Too many files"
  def error_to_string(:not_accepted), do: "File type not accepted"
  def error_to_string(error), do: "Error: #{inspect(error)}"

  # Shared UI component for variables table
  def variables_table(assigns) do
    ~H"""
    <div class="bg-base-100 p-4 rounded-lg border border-gray-200">
      <div class="flex justify-between items-center mb-2">
        <label class="label py-0">
          <span class="label-text">Variable</span>
        </label>
        <div class="flex space-x-2">
          <a
            href="https://www.google.com/help"
            target="_blank"
            class="btn btn-sm btn-ghost btn-circle"
          >
            <.icon name="hero-question-mark-circle" class="size-5" />
          </a>
          <button
            type="button"
            phx-click="clear_variables"
            phx-target={@myself}
            class="btn btn-sm btn-ghost btn-circle"
          >
            <.icon name="hero-trash" class="size-5" />
          </button>
        </div>
      </div>
      <div class="overflow-x-auto">
        <table class="table table-sm table-zebra w-full table-with-dividers">
          <tbody>
            <% # Get variables and check if they were recently cleared
            variables = Map.get(@edit_connection_set, :variables, [])
            # Get timestamp to force re-render when cleared
            _clear_timestamp = Map.get(@edit_connection_set, :_clear_timestamp, "")
            duplicate_colors = get_duplicate_colors(variables) %>
            <%= for {var, index} <- Enum.with_index(variables) do %>
              <% # Get color class for duplicate names
              color_class =
                if var.name != "" && Map.has_key?(duplicate_colors, var.name) do
                  Map.get(duplicate_colors, var.name)
                else
                  ""
                end %>
              <tr id={"variable-row-#{index}"}>
                <td class="px-2">
                  <input type="hidden" name={"variable[#{index}][enabled]"} value="on" />
                  <input
                    type="text"
                    name={"variable[#{index}][name]"}
                    value={var.name}
                    placeholder="Name"
                    class={"input input-sm w-full border-0 variable-text #{color_class}"}
                    id={"var-name-#{index}"}
                    phx-change="update_variable"
                    phx-target={@myself}
                  />
                  <%= if @is_edit do %>
                    <input type="hidden" name={"variable[#{index}][old_name]"} value={var.name} />
                  <% end %>
                </td>
                <td class="px-2">
                  <input
                    type="text"
                    name={"variable[#{index}][value]"}
                    value={var.value}
                    placeholder="Value"
                    class="input input-sm w-full border-0 variable-text"
                    id={"var-value-#{if @is_edit, do: "edit-", else: ""}#{index}"}
                    phx-change="update_variable"
                    phx-target={@myself}
                  />
                </td>
                <td class="px-2">
                  <div class="flex space-x-2">
                    <button
                      type="button"
                      phx-click="delete_variable"
                      phx-target={@myself}
                      phx-value-index={index}
                      class="btn btn-xs btn-ghost text-gray-500 p-0"
                    >
                      <.icon name="hero-minus-circle" class="size-4" />
                    </button>
                  </div>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
        <div class="mt-4 flex justify-left">
          <button
            type="button"
            phx-click="add_variable_row"
            phx-target={@myself}
            class="btn btn-sm btn-ghost text-gray-500 font-medium flex items-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                clip-rule="evenodd"
              />
            </svg>
            New Variable
          </button>
        </div>
      </div>
    </div>
    """
  end
end
