defmodule MqttableWeb.EnhancedPayloadEditorComponent do
  @moduledoc """
  Enhanced payload editor with advanced template helper functionality.

  Features:
  - Search and filter functions
  - Categorized function display
  - Expandable "show more" functionality
  - Support for both prefixed and clean function names
  """

  use MqttableWeb, :live_component
  alias Mqttable.Templating.Engine

  require Logger

  @impl true
  def mount(socket) do
    socket =
      socket
      |> assign_new(:payload, fn -> "" end)
      |> assign_new(:payload_format, fn -> "text" end)
      |> assign_new(:show_helper, fn -> false end)
      |> assign_new(:selected_category, fn -> "all" end)
      |> assign_new(:show_all_functions, fn -> false end)
      |> assign_new(:favorite_functions, fn -> MapSet.new() end)
      |> assign_new(:advanced_groups_expanded, fn -> %{} end)

    {:ok, socket}
  end

  @impl true
  def update(assigns, socket) do
    # Get payload and format from assigns or existing socket
    payload = Map.get(assigns, :payload, socket.assigns[:payload] || "")
    payload_format = Map.get(assigns, :payload_format, socket.assigns[:payload_format] || "text")

    # Initialize component state only if not already set
    socket =
      socket
      |> assign_new(:selected_category, fn -> "all" end)
      |> assign_new(:show_all_functions, fn -> false end)
      |> assign_new(:show_helper, fn -> false end)
      |> assign_new(:advanced_groups_expanded, fn -> %{} end)
      |> assign_new(:uploaded_file, fn -> nil end)
      |> assign_new(:file_preview, fn -> nil end)
      |> assign_new(:file_upload_error, fn -> nil end)

    # Update payload and format
    socket =
      socket
      |> assign(:payload, payload)
      |> assign(:payload_format, payload_format)
      |> assign(:preview_result, generate_preview(payload))

    # Assign other non-conflicting assigns
    other_assigns =
      Map.drop(assigns, [
        :payload,
        :payload_format,
        :selected_category,
        :show_all_functions,
        :show_helper,
        :advanced_groups_expanded
      ])

    socket = assign(socket, other_assigns)

    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div
      class="form-control w-full"
      phx-hook="PayloadEditor"
      id={"payload-editor-container-#{@myself}"}
    >
      <label class="label">
        <span class="label-text font-medium">{Map.get(assigns, :label, "Payload")}</span>
      </label>
      
    <!-- Format Selection -->
      <div class="flex items-center gap-3 mb-3">
        <div class="join">
          <input
            type="radio"
            name={"format-#{@myself}"}
            value="text"
            class="join-item btn btn-sm"
            aria-label="Text"
            checked={@payload_format == "text"}
            phx-click="format_changed"
            phx-value-format="text"
            phx-target={@myself}
          />
          <input
            type="radio"
            name={"format-#{@myself}"}
            value="json"
            class="join-item btn btn-sm"
            aria-label="JSON"
            checked={@payload_format == "json"}
            phx-click="format_changed"
            phx-value-format="json"
            phx-target={@myself}
          />
          <input
            type="radio"
            name={"format-#{@myself}"}
            value="hex"
            class="join-item btn btn-sm"
            aria-label="Hex"
            checked={@payload_format == "hex"}
            phx-click="format_changed"
            phx-value-format="hex"
            phx-target={@myself}
          />
        </div>
      </div>
      
    <!-- Payload Input -->
        <!-- File Upload Form -->
      <form phx-submit="upload_file" phx-change="file_selected" phx-target={@myself}>
        <!-- File Upload Area with Drag & Drop -->
        <section class="border-2 border-dashed border-base-300 rounded-lg p-6 text-center transition-colors hover:border-primary">
          <%= if @uploaded_file do %>
            <!-- File Information Display -->
            <div class="space-y-4">
              <div class="flex items-center justify-center gap-2 text-success">
                <.icon name="hero-document-check" class="size-6" />
                <span class="font-medium">File uploaded successfully</span>
              </div>

              <div class="bg-base-200 rounded-lg p-4 space-y-2">
                <div class="flex items-center justify-between">
                  <span class="text-sm font-medium">File:</span>
                  <span class="text-sm">{@uploaded_file.name}</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm font-medium">Size:</span>
                  <span class="text-sm">Unknown size</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm font-medium">Type:</span>
                  <span class="text-sm">{@uploaded_file.type}</span>
                </div>
              </div>
              
    <!-- Image Preview -->
              <%= if @file_preview do %>
                <div class="mt-4">
                  <div class="text-sm font-medium mb-2">Preview:</div>
                  <img
                    src={@file_preview}
                    alt="File preview"
                    class="max-w-full max-h-48 mx-auto rounded-lg border border-base-300"
                  />
                </div>
              <% end %>

              <button
                type="button"
                class="btn btn-sm btn-outline"
                phx-click="clear_file"
                phx-target={@myself}
              >
                <.icon name="hero-x-mark" class="size-4 mr-1" /> Clear File
              </button>
            </div>
          <% else %>
            <!-- File Upload Input -->
            <div class="space-y-4">
              <div>
                <p class="text-sm text-base-content/60 mt-1">
                  Supports all file types. Files will be converted to base64 for MQTT transmission.
                </p>
              </div>

              <input
                type="file"
                id={"file-input-#{@myself}"}
                class="file-input file-input-bordered file-input-primary w-full max-w-xs"
                phx-change="file_selected"
                phx-target={@myself}
              />
            </div>
          <% end %>
          
    <!-- File Upload Errors -->
          <%= if @file_upload_error do %>
            <p class="alert alert-error mt-3">{@file_upload_error}</p>
          <% end %>
        </section>
      </form>
      <!-- Regular Textarea -->
      <textarea
        id={"payload-editor-#{@myself}"}
        name="payload"
        placeholder={get_placeholder(@payload_format)}
        class="textarea textarea-bordered w-full h-32 payload-textarea font-mono"
        phx-change="payload_changed"
        phx-target={@myself}
      ><%= @payload %></textarea>
      
    <!-- Live Preview -->
      <%= if @show_helper and (String.contains?(@payload, "{{") || String.contains?(@payload, "{%")) do %>
        <div class="mt-3">
          <div class="text-sm font-medium mb-2 flex items-center gap-2">
            <.icon name="hero-eye" class="size-4" /> Live Preview:
          </div>
          <div class="bg-base-100 border border-base-300 rounded-lg p-3 text-sm font-mono preview-content">
            <%= case @preview_result do %>
              <% {:ok, result} -> %>
                <pre class="whitespace-pre-wrap text-success"><%= result %></pre>
              <% {:error, error} -> %>
                <div class="text-error">Error: {error}</div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  # Event Handlers

  @impl true
  def handle_event("payload_changed", %{"value" => payload}, socket) do
    socket =
      socket
      |> assign(:payload, payload)
      |> assign(:preview_result, generate_preview(payload))

    # Notify parent component
    send(self(), {:payload_editor_changed, payload, socket.assigns.payload_format})

    {:noreply, socket}
  end

  @impl true
  def handle_event("payload_changed", %{"payload" => payload}, socket) do
    # Handle alternative event format from textarea
    socket =
      socket
      |> assign(:payload, payload)
      |> assign(:preview_result, generate_preview(payload))

    # Notify parent component
    send(self(), {:payload_editor_changed, payload, socket.assigns.payload_format})

    {:noreply, socket}
  end

  @impl true
  def handle_event("format_changed", %{"format" => format}, socket) do
    socket = assign(socket, :payload_format, format)

    # Notify parent component
    send(self(), {:payload_editor_changed, socket.assigns.payload, format})

    {:noreply, socket}
  end

  @impl true
  def handle_event("toggle_helper", _params, socket) do
    {:noreply, assign(socket, :show_helper, !socket.assigns.show_helper)}
  end

  @impl true
  def handle_event("filter_category", %{"category" => category}, socket) do
    {:noreply, assign(socket, :selected_category, category)}
  end

  @impl true
  def handle_event("filter_category", %{"value" => category}, socket) do
    {:noreply, assign(socket, :selected_category, category)}
  end

  @impl true
  def handle_event("filter_category", params, socket) do
    # Fallback handler for malformed events
    IO.inspect(params, label: "Unexpected filter_category params")
    {:noreply, socket}
  end

  @impl true
  def handle_event("toggle_show_all", _params, socket) do
    {:noreply, assign(socket, :show_all_functions, !socket.assigns.show_all_functions)}
  end

  @impl true
  def handle_event("toggle_advanced_group", %{"group" => group_key}, socket) do
    current_expanded = socket.assigns.advanced_groups_expanded

    new_expanded =
      Map.put(current_expanded, group_key, !Map.get(current_expanded, group_key, false))

    {:noreply, assign(socket, :advanced_groups_expanded, new_expanded)}
  end

  @impl true
  def handle_event("insert_template", %{"template" => template}, socket) do
    # Push event to JavaScript to insert at cursor position
    {:noreply,
     push_event(socket, "insert_at_cursor", %{
       target_id: "payload-editor-#{socket.assigns.myself}",
       text: template
     })}
  end

  @impl true
  def handle_event("toggle_favorite", %{"function" => function_name}, socket) do
    favorites = socket.assigns.favorite_functions

    new_favorites =
      if function_name in favorites do
        MapSet.delete(favorites, function_name)
      else
        MapSet.put(favorites, function_name)
      end

    {:noreply, assign(socket, :favorite_functions, new_favorites)}
  end

  # Helper Functions

  defp generate_preview(payload) do
    if String.contains?(payload, "{{") || String.contains?(payload, "{%") do
      case Engine.preview(payload) do
        {:ok, result} -> {:ok, result}
        {:error, error} -> {:error, error}
      end
    else
      {:ok, payload}
    end
  end

  defp get_placeholder("json"), do: "Enter JSON payload or use {{ template_function }} syntax"
  defp get_placeholder("hex"), do: "Enter hex payload (e.g., 48656C6C6F)"
  defp get_placeholder(_), do: "Enter payload or use {{ template_function }} syntax"
end
