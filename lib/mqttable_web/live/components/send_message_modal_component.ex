defmodule MqttableWeb.SendMessageModalComponent do
  @moduledoc """
  A reusable modal component for sending MQTT messages.

  This component provides a form for composing and sending MQTT messages with support for:
  - Client selection with automatic fallback
  - Form state persistence across modal open/close cycles
  - MQTT 5.0 properties and user properties
  - Click-outside-to-close functionality
  """

  use MqttableWeb, :live_component
  import MqttableWeb.Shared.MessageFormComponents

  require Logger

  @impl true
  def mount(socket) do
    socket =
      socket
      |> assign_new(:show_modal, fn -> false end)
      |> assign_new(:publish_form, fn -> default_publish_form() end)
      |> assign_new(:alert_message, fn -> nil end)
      |> assign_new(:alert_type, fn -> nil end)
      |> assign_new(:uploaded_file, fn -> nil end)

    {:ok, socket}
  end

  @impl true
  def update(assigns, socket) do
    Logger.debug(
      "SendMessageModalComponent update called with assigns keys: #{inspect(Map.keys(assigns))}"
    )

    # If modal is open and this update is not related to modal state changes,
    # skip the update to prevent disrupting user interaction
    if socket.assigns[:show_modal] && assigns[:show_modal] &&
         not Map.has_key?(assigns, :form_state) &&
         socket.assigns[:active_broker_name] == assigns[:active_broker_name] do
      Logger.debug(
        "Modal is open and update is not form-related, skipping to preserve user input"
      )

      {:ok, socket}
    else
      # Store the broker name when modal is shown, so we can use it when closing
      current_broker_name =
        if assigns[:show_modal] && assigns[:active_broker_name] do
          assigns[:active_broker_name]
        else
          socket.assigns[:stored_broker_name]
        end

      # Use form state from parent if provided, otherwise use current or default
      current_form =
        assigns[:form_state] ||
          socket.assigns[:publish_form] ||
          default_publish_form()

      Logger.debug("Current form state: #{inspect(current_form)}")

      # Smart client selection logic
      updated_form =
        if assigns[:show_modal] do
          smart_client_selection(current_form, assigns[:active_broker_name])
        else
          current_form
        end

      Logger.debug("After smart client selection: #{inspect(updated_form)}")

      # If smart client selection changed the client_id, save the updated form state
      if updated_form != current_form && assigns[:show_modal] do
        Logger.debug("Smart client selection changed form, saving updated state")
        cleaned_form = clean_form_for_storage(updated_form)
        send(self(), {:update_send_modal_form, cleaned_form, current_broker_name})
      end

      # Load MQTT 5.0 properties collapse state from ui_state
      mqtt5_collapsed = get_mqtt5_properties_collapsed_state(assigns[:active_broker_name])

      # Get connected clients for the active broker
      connected_clients = get_connected_clients(assigns[:active_broker_name] || "")

      # Simplified form update - no complex template logic needed

      socket =
        socket
        |> assign(assigns)
        |> assign(:publish_form, updated_form)
        |> assign(:mqtt5_properties_collapsed, mqtt5_collapsed)
        |> assign(:stored_broker_name, current_broker_name)
        |> assign(:connected_clients, connected_clients)

      {:ok, socket}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <!-- Send Message Modal with Sidebar Layout -->
      <dialog
        id="send-message-modal"
        class={"modal #{if @show_modal, do: "modal-open", else: ""}"}
        style={if @show_modal, do: "", else: "display: none;"}
      >
        <div class="modal-backdrop" phx-click="close_send_modal"></div>
        <div
          class="modal-box max-w-6xl ml-auto mr-6 mt-6 mb-6 h-[calc(100vh-3rem)] flex flex-col send-message-modal-sidebar"
          id="send-message-modal-content"
        >
          <!-- Modal Header -->
          <div class="flex items-center justify-between mb-4 flex-shrink-0">
            <h3 class="text-lg font-semibold flex items-center">
              <.icon name="hero-paper-airplane" class="size-5 mr-2" /> Send MQTT Message
            </h3>
            <button class="btn btn-sm btn-circle btn-ghost" phx-click="close_send_modal">
              ✕
            </button>
          </div>
          
    <!-- Alert Message -->
          <div :if={@alert_message} class="mb-4 flex-shrink-0">
            <div
              role="alert"
              class={[
                "alert",
                @alert_type == :success && "alert-success",
                @alert_type == :error && "alert-error",
                @alert_type == :warning && "alert-warning"
              ]}
            >
              <svg
                :if={@alert_type == :success}
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 shrink-0 stroke-current"
                fill="none"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <svg
                :if={@alert_type == :error}
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 shrink-0 stroke-current"
                fill="none"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <svg
                :if={@alert_type == :warning}
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 shrink-0 stroke-current"
                fill="none"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                />
              </svg>
              <span>{@alert_message}</span>
              <button
                type="button"
                class="btn btn-sm btn-ghost ml-auto"
                phx-click="dismiss_alert"
                phx-target={@myself}
              >
                ✕
              </button>
            </div>
          </div>
          
    <!-- Modal Content with Sidebar Layout -->
          <div
            class="flex-1 overflow-hidden"
            phx-hook="PayloadEditor"
            id={"send-payload-container-#{@myself}"}
          >
            <div class="h-full flex gap-6">
              <!-- Left Column: Main Form (60%) -->
              <div class="flex-1 overflow-y-auto pr-2">
                <.form
                  for={%{}}
                  as={:publish}
                  phx-submit="send_message"
                  phx-change="form_changed"
                  phx-target={@myself}
                  class="space-y-4"
                  id="publish-form"
                >
                  <!-- Client Selection -->
                  <.client_selection
                    form={@publish_form}
                    connected_clients={@connected_clients}
                    active_broker_name={@active_broker_name}
                    myself={@myself}
                    label="Client"
                  />
                  
    <!-- Topic Input -->
                  <div class="form-control w-full">
                    <label class="label">
                      <span class="label-text font-medium">Topic</span>
                    </label>
                    <label class="input input-bordered flex items-center gap-2">
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 1024 1024"
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                        p-id="14538"
                      >
                        <path
                          d="M512 85.333333c235.648 0 426.666667 191.018667 426.666667 426.666667s-191.018667 426.666667-426.666667 426.666667S85.333333 747.648 85.333333 512 276.352 85.333333 512 85.333333z m0 85.333334C323.477333 170.666667 170.666667 323.477333 170.666667 512s152.810667 341.333333 341.333333 341.333333 341.333333-152.810667 341.333333-341.333333S700.522667 170.666667 512 170.666667z m0 128c23.552 0 42.666667 19.114667 42.666667 42.666666v128h128c23.552 0 42.666667 19.114667 42.666667 42.666667s-19.114667 42.666667-42.666667 42.666667h-128v128c0 23.552-19.114667 42.666667-42.666667 42.666667s-42.666667-19.114667-42.666667-42.666667v-128h-128c-23.552 0-42.666667-19.114667-42.666667-42.666667s19.114667-42.666667 42.666667-42.666667h128v-128c0-23.552 19.114667-42.666667 42.666667-42.666667z"
                          fill="#172B4D"
                          p-id="14539"
                        >
                        </path>
                      </svg>
                      <input
                        type="text"
                        name="topic"
                        value={@publish_form["topic"]}
                        placeholder="Topic (e.g., 'device/sensor/temperature')"
                        class="grow"
                      />
                    </label>
                  </div>
                  
    <!-- Payload Input with File Upload -->
                  <.payload_input_with_file_upload
                    form={@publish_form}
                    myself={@myself}
                    uploaded_file={@uploaded_file}
                    label="Payload"
                  />
                  
    <!-- QoS, Retain Message, and Send Message Button Row (3 equal parts) -->
                  <div class="grid grid-cols-3 gap-4 items-center">
                    <!-- QoS Selection (1/3) -->
                    <div class="flex justify-start">
                      <.qos_selection form={@publish_form} myself={@myself} />
                    </div>
                    
    <!-- Retain Message (1/3) -->
                    <div class="flex justify-center">
                      <.retain_checkbox form={@publish_form} label="Retain Message" />
                    </div>
                    
    <!-- Send Message Button (1/3) -->
                    <div class="flex justify-end">
                      <button type="submit" class="btn btn-primary">
                        <.icon name="hero-paper-airplane" class="size-4 mr-2" /> Send Message
                      </button>
                    </div>
                  </div>
                  
    <!-- MQTT 5.0 Properties Section -->
                  <.mqtt5_properties_section
                    form={@publish_form}
                    myself={@myself}
                    collapsed={@mqtt5_properties_collapsed}
                    show_properties={
                      show_mqtt5_properties?(@publish_form["client_id"], @active_broker_name)
                    }
                  />
                </.form>
              </div>
              
    <!-- Right Column: Template Helper Sidebar (40%) -->
              <div class="w-2/5 border-l border-base-300 pl-6 overflow-y-auto">
                <.live_component
                  module={MqttableWeb.TwoTabTemplateHelperComponent}
                  id={"two-tab-template-helper-#{@myself}"}
                  target_textarea_id={"enhanced-payload-editor-send-#{@myself}"}
                  payload={@publish_form["payload"] || ""}
                  payload_format={@publish_form["payload_format"] || "text"}
                  active_broker_name={@active_broker_name}
                />
              </div>
            </div>
          </div>
        </div>
      </dialog>
    </div>
    """
  end

  # Event Handlers

  @impl true
  def handle_event("form_changed", params, socket) do
    require Logger
    Logger.debug("SendMessageModalComponent: form_changed event received")
    Logger.debug("SendMessageModalComponent: form_changed params: #{inspect(params)}")

    # Regular form change handling
    # Extract form parameters from the publish namespace
    publish_params = params["publish"] || params
    # Update form state with all current values from the form
    updated_form = update_form_with_params(socket.assigns.publish_form, publish_params)
    # Validate payload based on current format
    validated_form = validate_payload_in_form(updated_form)

    # Clean and notify parent component about form state change
    cleaned_form = clean_form_for_storage(validated_form)
    broker_name = socket.assigns[:stored_broker_name]
    send(self(), {:update_send_modal_form, cleaned_form, broker_name})

    {:noreply, assign(socket, :publish_form, validated_form)}
  end

  @impl true
  def handle_event("user_property_changed", params, socket) do
    # Extract user properties from the form parameters
    current_properties = socket.assigns.publish_form["user_properties"] || []

    # Parse the user property fields from params
    updated_properties = parse_user_properties_from_params(params, current_properties)

    # Preserve all existing form fields, only update user_properties
    updated_form = Map.put(socket.assigns.publish_form, "user_properties", updated_properties)

    # Clean and notify parent component about form state change
    cleaned_form = clean_form_for_storage(updated_form)
    broker_name = socket.assigns[:stored_broker_name]
    send(self(), {:update_send_modal_form, cleaned_form, broker_name})
    {:noreply, assign(socket, :publish_form, updated_form)}
  end

  @impl true
  def handle_event("add_user_property", _params, socket) do
    current_properties = socket.assigns.publish_form["user_properties"] || []
    new_properties = current_properties ++ [%{"key" => "", "value" => ""}]

    # Preserve all existing form fields, only update user_properties
    updated_form = Map.put(socket.assigns.publish_form, "user_properties", new_properties)

    # Clean and notify parent component about form state change
    cleaned_form = clean_form_for_storage(updated_form)
    broker_name = socket.assigns[:stored_broker_name]
    send(self(), {:update_send_modal_form, cleaned_form, broker_name})
    {:noreply, assign(socket, :publish_form, updated_form)}
  end

  @impl true
  def handle_event("remove_user_property", %{"index" => index_str}, socket) do
    index = String.to_integer(index_str)
    current_properties = socket.assigns.publish_form["user_properties"] || []

    new_properties = List.delete_at(current_properties, index)

    # Preserve all existing form fields, only update user_properties
    updated_form = Map.put(socket.assigns.publish_form, "user_properties", new_properties)

    # Clean and notify parent component about form state change
    cleaned_form = clean_form_for_storage(updated_form)
    broker_name = socket.assigns[:stored_broker_name]
    send(self(), {:update_send_modal_form, cleaned_form, broker_name})
    {:noreply, assign(socket, :publish_form, updated_form)}
  end

  @impl true
  def handle_event("toggle_mqtt5_properties", _params, socket) do
    broker_name = socket.assigns[:active_broker_name]
    current_collapsed = socket.assigns[:mqtt5_properties_collapsed] || false
    new_collapsed = !current_collapsed

    # Save the collapse state to ui_state
    if broker_name do
      save_mqtt5_properties_collapsed_state(broker_name, new_collapsed)
    end

    {:noreply, assign(socket, :mqtt5_properties_collapsed, new_collapsed)}
  end

  @impl true
  def handle_event("send_message", params, socket) do
    require Logger
    Logger.debug("SendMessageModalComponent: Received send_message event")
    Logger.debug("SendMessageModalComponent: Full params: #{inspect(params)}")

    Logger.debug(
      "SendMessageModalComponent: Current form state: #{inspect(socket.assigns.publish_form)}"
    )

    Logger.debug(
      "SendMessageModalComponent: Uploaded file state: #{inspect(socket.assigns.uploaded_file)}"
    )

    # Extract form parameters from the publish namespace
    publish_params = params["publish"] || params
    client_id = publish_params["client_id"]
    topic = publish_params["topic"]

    # Get payload and format from component state instead of form params
    payload = socket.assigns.publish_form["payload"] || ""
    payload_format = socket.assigns.publish_form["payload_format"] || "text"

    Logger.debug(
      "SendMessageModalComponent: Extracted - client_id: #{inspect(client_id)}, topic: #{inspect(topic)}, payload length: #{String.length(payload || "")}, format: #{payload_format}"
    )

    qos = String.to_integer(publish_params["qos"] || "0")
    retain = publish_params["retain"] == "on"

    # Process payload with new simplified logic
    {final_payload, payload_error} = process_payload(payload, socket.assigns[:active_broker_name])

    # Update form state with all current values (including MQTT 5.0 properties)
    updated_form = update_form_with_params(socket.assigns.publish_form, publish_params)

    # Validate payload AFTER template evaluation
    validated_form =
      validate_payload_in_form_after_template(updated_form, final_payload, payload_format)

    socket = assign(socket, :publish_form, validated_form)

    # Validate required fields and payload format
    if client_id != "" && topic != "" && validated_form["payload_validation_error"] == nil &&
         payload_error == nil do
      # Use final payload (either template-generated or manual)
      actual_payload = if payload_error == nil, do: final_payload, else: payload

      # Encode payload based on format
      file_encoding = params["file_encoding"] || "binary"

      case encode_payload_for_transmission(actual_payload, payload_format, file_encoding) do
        {:ok, encoded_payload} ->
          # Build MQTT 5.0 properties if the client supports it
          connected_clients = get_connected_clients(socket.assigns[:active_broker_name] || "")
          properties = build_mqtt5_publish_properties(params, connected_clients, client_id)

          # Prepare publish options
          publish_opts = [qos: qos, retain: retain]

          # Add properties if any
          publish_opts =
            if map_size(properties) > 0 do
              [{:properties, properties} | publish_opts]
            else
              publish_opts
            end

          # Attempt to publish the message with encoded payload
          case Mqttable.MqttClient.Manager.publish(
                 client_id,
                 topic,
                 encoded_payload,
                 publish_opts
               ) do
            {:ok, packet_id} ->
              # Success - show success alert in modal and keep modal open
              {message, _flash_type} = format_publish_result(packet_id)
              timestamped_message = add_timestamp_to_message(message)

              socket =
                socket
                |> assign(:alert_message, timestamped_message)
                |> assign(:alert_type, :success)

              {:noreply, socket}

            {:error, :not_connected} ->
              timestamped_message = add_timestamp_to_message("Client is not connected")

              socket =
                socket
                |> assign(:alert_message, timestamped_message)
                |> assign(:alert_type, :error)

              {:noreply, socket}

            {:error, _reason, error_message} ->
              timestamped_message =
                add_timestamp_to_message("Failed to send message: #{error_message}")

              socket =
                socket
                |> assign(:alert_message, timestamped_message)
                |> assign(:alert_type, :error)

              {:noreply, socket}
          end

        {:error, encoding_error} ->
          timestamped_message =
            add_timestamp_to_message("Payload encoding failed: #{encoding_error}")

          socket =
            socket
            |> assign(:alert_message, timestamped_message)
            |> assign(:alert_type, :error)

          {:noreply, socket}
      end
    else
      # Validation failed - check what specifically failed
      error_message =
        cond do
          client_id == "" ->
            "Please select a client"

          topic == "" ->
            "Please enter a topic"

          payload_error != nil ->
            payload_error

          validated_form["payload_validation_error"] != nil ->
            "Payload validation failed: #{validated_form["payload_validation_error"]}"

          true ->
            "Please fill in all required fields"
        end

      timestamped_message = add_timestamp_to_message(error_message)

      socket =
        socket
        |> assign(:alert_message, timestamped_message)
        |> assign(:alert_type, :error)

      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("select_client", %{"client_id" => client_id} = _params, socket) do
    # Update the client_id in the form
    updated_form = Map.put(socket.assigns.publish_form, "client_id", client_id)

    # Clean and notify parent component about form state change
    cleaned_form = clean_form_for_storage(updated_form)
    broker_name = socket.assigns[:stored_broker_name]
    send(self(), {:update_send_modal_form, cleaned_form, broker_name})

    {:noreply, assign(socket, :publish_form, updated_form)}
  end

  @impl true
  def handle_event("dismiss_alert", _params, socket) do
    socket =
      socket
      |> assign(:alert_message, nil)
      |> assign(:alert_type, nil)

    {:noreply, socket}
  end

  @impl true
  def handle_event("clear_file", _params, socket) do
    socket =
      socket
      |> assign(:uploaded_file, nil)
      |> update(:publish_form, fn form ->
        form
        |> Map.put("payload", "")
        |> Map.put("payload_format", "text")
      end)

    {:noreply, socket}
  end

  @impl true
  def handle_event("format_changed", %{"format" => format}, socket) do
    socket =
      socket
      |> assign(:uploaded_file, nil)
      |> update(:publish_form, fn form ->
        form
        |> Map.put("payload_format", format)
        |> Map.put("payload", if(format == "file", do: "", else: form["payload"]))
        |> Map.put(
          "file_encoding",
          if(format == "file", do: form["file_encoding"] || "binary", else: nil)
        )
      end)

    {:noreply, socket}
  end

  @impl true
  def handle_event("file_encoding_changed", %{"encoding" => encoding}, socket) do
    socket =
      socket
      |> update(:publish_form, fn form ->
        Map.put(form, "file_encoding", encoding)
      end)

    {:noreply, socket}
  end

  @impl true
  def handle_event("validate", params, socket) do
    require Logger

    Logger.debug(
      "SendMessageModalComponent: validate event received with params: #{inspect(params)}"
    )

    # Just validate the form, don't process files yet
    {:noreply, socket}
  end

  # File upload is now handled directly in JavaScript, no LiveView events needed

  # Handle info messages

  # UI Components

  attr :form, :map, required: true
  attr :myself, :any, required: true
  attr :uploaded_file, :map, default: nil
  attr :label, :string, default: "Payload"

  def payload_input_with_file_upload(assigns) do
    ~H"""
    <div class="form-control w-full">
      <label class="label">
        <span class="label-text font-medium">{@label}</span>
      </label>
      
    <!-- Format Selection using DaisyUI Tab Lift -->
      <div class="tabs tabs-lift mb-4">
        <label class="tab">
          <input
            type="radio"
            name="payload_format"
            value="text"
            checked={@form["payload_format"] == "text" || @form["payload_format"] == nil}
            phx-click="format_changed"
            phx-value-format="text"
            phx-target={@myself}
          />
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4 me-2"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z"
            />
          </svg>
          Text
        </label>

        <label class="tab">
          <input
            type="radio"
            name="payload_format"
            value="json"
            checked={@form["payload_format"] == "json"}
            phx-click="format_changed"
            phx-value-format="json"
            phx-target={@myself}
          />
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4 me-2"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M17.25 6.75 22.5 12l-5.25 5.25m-10.5 0L1.5 12l5.25-5.25m7.5-3-4.5 16.5"
            />
          </svg>
          JSON
        </label>

        <label class="tab">
          <input
            type="radio"
            name="payload_format"
            value="hex"
            checked={@form["payload_format"] == "hex"}
            phx-click="format_changed"
            phx-value-format="hex"
            phx-target={@myself}
          />
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4 me-2"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M5.25 14.25h13.5m-13.5 0a3 3 0 0 1-3-3V6a3 3 0 0 1 3-3h13.5a3 3 0 0 1 3 3v5.25a3 3 0 0 1-3 3m-16.5 0a3 3 0 0 1 3-3h13.5a3 3 0 0 1 3 3v6a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3v-6Z"
            />
          </svg>
          Hex
        </label>

        <label class="tab">
          <input
            type="radio"
            name="payload_format"
            value="file"
            checked={@form["payload_format"] == "file"}
            phx-click="format_changed"
            phx-value-format="file"
            phx-target={@myself}
          />
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4 me-2"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"
            />
          </svg>
          File
        </label>
      </div>
      
    <!-- Content Area -->
      <%= if @form["payload_format"] == "file" do %>
        <!-- Hidden payload input for JavaScript to update -->
        <textarea name="payload" value={@form["payload"]} class="hidden"><%= @form["payload"] %></textarea>
        
    <!-- File Upload Card -->
        <div class="card bg-base-100 border border-base-300 shadow-sm">
          <div class="card-body p-4">
            <!-- File Encoding Selection -->
            <div class="mb-4">
              <div class="flex items-center gap-2 mb-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="size-4"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.*************.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a6.759 6.759 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"
                  />
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                  />
                </svg>
                <span class="text-sm font-medium">Encoding</span>
              </div>

              <div class="join">
                <input
                  type="radio"
                  name="file_encoding"
                  value="binary"
                  checked={@form["file_encoding"] == "binary" || @form["file_encoding"] == nil}
                  class="join-item btn btn-sm btn-outline"
                  aria-label="Binary"
                  phx-click="file_encoding_changed"
                  phx-value-encoding="binary"
                  phx-target={@myself}
                />
                <input
                  type="radio"
                  name="file_encoding"
                  value="base64"
                  checked={@form["file_encoding"] == "base64"}
                  class="join-item btn btn-sm btn-outline"
                  aria-label="Base64"
                  phx-click="file_encoding_changed"
                  phx-value-encoding="base64"
                  phx-target={@myself}
                />
              </div>

              <div class="text-xs text-base-content/60 mt-2 flex items-center gap-1">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="size-3"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"
                  />
                </svg>
                <%= if @form["file_encoding"] == "base64" do %>
                  Text-compatible format, larger size (~33% increase)
                <% else %>
                  Raw binary format, original file size
                <% end %>
              </div>
            </div>
            
    <!-- File Upload Area -->
            <%= if @uploaded_file do %>
              <!-- File Information Display -->
              <div class="bg-success/10 border border-success/20 rounded-lg p-4">
                <div class="flex items-center gap-2 text-success mb-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="size-5"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                    />
                  </svg>
                  <span class="font-medium">File uploaded successfully</span>
                </div>

                <div class="grid grid-cols-2 gap-2 text-sm mb-3">
                  <div><span class="text-base-content/60">Name:</span> {@uploaded_file.name}</div>
                  <div>
                    <span class="text-base-content/60">Size:</span> {format_file_size(
                      @uploaded_file.size
                    )}
                  </div>
                  <div><span class="text-base-content/60">Type:</span> {@uploaded_file.type}</div>
                  <%= if @form["file_encoding"] == "base64" do %>
                    <div>
                      <span class="text-base-content/60">Encoded:</span> {format_file_size(
                        calculate_base64_size(@uploaded_file.size)
                      )}
                    </div>
                  <% end %>
                </div>

                <button
                  type="button"
                  class="btn btn-sm btn-outline btn-error"
                  phx-click="clear_file"
                  phx-target={@myself}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="size-4 mr-1"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
                  </svg>
                  Clear File
                </button>
              </div>
            <% else %>
              <!-- File Upload Input -->
              <div class="border-2 border-dashed border-base-300 rounded-lg p-6 text-center hover:border-primary/50 transition-colors">
                <div class="flex flex-col items-center gap-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="size-8 text-base-content/40"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5"
                    />
                  </svg>
                  <div>
                    <p class="text-sm font-medium mb-1">Choose a file to upload</p>
                    <p class="text-xs text-base-content/60">Maximum size: 16MB</p>
                  </div>
                  <input
                    type="file"
                    class="file-input file-input-bordered file-input-primary file-input-sm w-full max-w-xs"
                    id="file-upload-input"
                    phx-hook="FileUpload"
                    phx-target={@myself}
                  />
                </div>
              </div>
            <% end %>
          </div>
        </div>
      <% else %>
        <!-- Text/JSON/Hex Input -->
        <textarea
          id={"enhanced-payload-editor-send-#{@myself}"}
          name="payload"
          value={@form["payload"]}
          placeholder={get_payload_placeholder(@form["payload_format"])}
          class="textarea textarea-bordered w-full min-h-[120px]"
          spellcheck="false"
        ><%= @form["payload"] %></textarea>
      <% end %>
      
    <!-- Validation Error -->
      <%= if @form["payload_validation_error"] do %>
        <div class="mt-2">
          <div class="alert alert-error alert-sm">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="size-4"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z"
              />
            </svg>
            <span>{@form["payload_validation_error"]}</span>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  # Helper Functions

  defp get_payload_placeholder(format) do
    case format do
      "json" -> "Enter JSON payload..."
      "hex" -> "Enter hex payload (e.g., 48656c6c6f)"
      "file" -> ""
      _ -> "Enter payload..."
    end
  end

  defp format_file_size(size) when is_integer(size) do
    cond do
      size >= 1_048_576 -> "#{Float.round(size / 1_048_576, 1)} MB"
      size >= 1024 -> "#{Float.round(size / 1024, 1)} KB"
      true -> "#{size} B"
    end
  end

  defp format_file_size(_), do: "Unknown size"

  defp calculate_base64_size(original_size) when is_integer(original_size) do
    # Base64 encoding increases size by approximately 4/3 (33%)
    # Plus padding for alignment
    trunc(original_size * 4 / 3) + 4
  end

  defp calculate_base64_size(_), do: 0

  defp show_mqtt5_properties?(client_id, active_broker_name) do
    # Show MQTT 5.0 properties if client supports MQTT 5.0
    if client_id != "" do
      connected_clients = get_connected_clients(active_broker_name || "")

      case Enum.find(connected_clients, fn client -> client.client_id == client_id end) do
        %{mqtt_version: version} when version in ["5.0", "5"] -> true
        _ -> false
      end
    else
      false
    end
  end

  defp validate_payload_in_form(form) do
    payload = Map.get(form, "payload", "")
    format = Map.get(form, "payload_format", "text")

    case validate_payload(payload, format) do
      {:ok, _} ->
        Map.put(form, "payload_validation_error", nil)

      {:error, error_message} ->
        Map.put(form, "payload_validation_error", error_message)
    end
  end

  defp validate_payload_in_form_after_template(form, final_payload, payload_format) do
    case validate_payload(final_payload, payload_format) do
      {:ok, _} ->
        Map.put(form, "payload_validation_error", nil)

      {:error, error_message} ->
        Map.put(form, "payload_validation_error", error_message)
    end
  end

  defp validate_payload(payload, format) do
    case format do
      "json" -> validate_json_payload(payload)
      "hex" -> validate_hex_payload(payload)
      "file" -> validate_file_payload(payload)
      "text" -> {:ok, payload}
      _ -> {:ok, payload}
    end
  end

  defp validate_file_payload(""),
    do: {:error, "Please upload a file or select a different format"}

  defp validate_file_payload(payload) when is_binary(payload) do
    # Check if payload looks like base64 (basic validation)
    if String.length(payload) > 0 and String.match?(payload, ~r/^[A-Za-z0-9+\/]*={0,2}$/) do
      case Base.decode64(payload) do
        {:ok, _decoded} -> {:ok, payload}
        :error -> {:error, "Invalid file content. Please upload the file again."}
      end
    else
      {:error, "Please upload a file or select a different format"}
    end
  end

  defp validate_json_payload(""), do: {:ok, ""}

  defp validate_json_payload(payload) when is_binary(payload) do
    case Jason.decode(payload) do
      {:ok, _} -> {:ok, payload}
      {:error, _} -> {:error, "Invalid JSON format"}
    end
  end

  defp validate_hex_payload(""), do: {:ok, ""}

  defp validate_hex_payload(payload) when is_binary(payload) do
    # Remove whitespace and check for valid hex
    cleaned = String.replace(payload, ~r/\s/, "")

    cond do
      cleaned == "" ->
        {:ok, payload}

      not String.match?(cleaned, ~r/^[0-9A-Fa-f]*$/) ->
        {:error, "Invalid hex format. Use only 0-9, A-F characters"}

      rem(String.length(cleaned), 2) != 0 ->
        {:error, "Hex payload must have even number of characters"}

      true ->
        {:ok, payload}
    end
  end

  defp encode_payload_for_transmission(payload, format, file_encoding) do
    case format do
      "hex" ->
        encode_hex_payload(payload)

      "file" ->
        case file_encoding do
          "base64" ->
            # Send file as base64-encoded text
            {:ok, payload}

          _ ->
            # Default: decode base64 to binary for MQTT transmission
            case Base.decode64(payload) do
              {:ok, binary} -> {:ok, binary}
              :error -> {:error, "Failed to decode file payload"}
            end
        end

      _ ->
        {:ok, payload}
    end
  end

  defp encode_hex_payload(""), do: {:ok, ""}

  defp encode_hex_payload(payload) when is_binary(payload) do
    # Remove whitespace and decode hex to binary
    cleaned = String.replace(payload, ~r/\s/, "")

    case Base.decode16(cleaned, case: :mixed) do
      {:ok, binary} -> {:ok, binary}
      :error -> {:error, "Failed to decode hex payload"}
    end
  end

  defp clean_form_for_storage(form_state) when is_map(form_state) do
    # Remove temporary form fields and Phoenix internal fields that shouldn't be persisted
    cleaned_form =
      form_state
      |> Enum.reject(fn {key, _value} ->
        # Convert key to string to handle both atom and string keys
        key_str = to_string(key)

        String.starts_with?(key_str, "user_property_key_") or
          String.starts_with?(key_str, "user_property_value_") or
          String.starts_with?(key_str, "_target") or
          String.starts_with?(key_str, "_unused_")
      end)
      |> Enum.into(%{}, fn {key, value} ->
        # Ensure all keys are strings for consistency
        {to_string(key), value}
      end)

    # Filter out empty user properties to prevent empty property boxes on page refresh
    user_properties = Map.get(cleaned_form, "user_properties", [])

    filtered_user_properties =
      Enum.filter(user_properties, fn property ->
        key = Map.get(property, "key", "")
        value = Map.get(property, "value", "")
        # Keep property only if at least key or value is not empty
        key != "" || value != ""
      end)

    Map.put(cleaned_form, "user_properties", filtered_user_properties)
  end

  defp default_publish_form do
    %{
      "client_id" => "",
      "topic" => "",
      "payload" => "",
      "payload_format" => "text",
      "payload_validation_error" => nil,
      "qos" => "0",
      "retain" => false,
      # MQTT 5.0 properties - use proper data types
      "content_type" => "",
      "payload_format_indicator" => false,
      "message_expiry_interval" => 0,
      "topic_alias" => 0,
      "response_topic" => "",
      "correlation_data" => "",
      "user_properties" => []
    }
  end

  defp update_form_with_params(current_form, params) do
    # Update form with all parameters, handling type conversions
    updated_form =
      Enum.reduce(params, current_form, fn {key, value}, acc ->
        case key do
          "qos" ->
            # Keep QoS as string for UI consistency
            Map.put(acc, key, value)

          "retain" ->
            Map.put(acc, key, value == "on")

          "payload_format_indicator" ->
            Map.put(acc, key, value == "on")

          "message_expiry_interval" ->
            # Convert to integer, default to 0 if empty or invalid
            case value do
              "" ->
                Map.put(acc, key, 0)

              val when is_binary(val) ->
                case Integer.parse(val) do
                  {int_val, ""} when int_val >= 0 -> Map.put(acc, key, int_val)
                  _ -> Map.put(acc, key, 0)
                end

              val when is_integer(val) ->
                Map.put(acc, key, val)

              _ ->
                Map.put(acc, key, 0)
            end

          "topic_alias" ->
            # Convert to integer, default to 0 if empty or invalid
            case value do
              "" ->
                Map.put(acc, key, 0)

              val when is_binary(val) ->
                case Integer.parse(val) do
                  {int_val, ""} when int_val >= 0 and int_val <= 65535 ->
                    Map.put(acc, key, int_val)

                  _ ->
                    Map.put(acc, key, 0)
                end

              val when is_integer(val) ->
                Map.put(acc, key, val)

              _ ->
                Map.put(acc, key, 0)
            end

          _ ->
            Map.put(acc, key, value)
        end
      end)

    # Handle checkboxes that don't send values when unchecked
    final_form =
      updated_form
      |> Map.put("retain", Map.get(params, "retain") == "on")
      |> Map.put("payload_format_indicator", Map.get(params, "payload_format_indicator") == "on")

    # Handle user properties separately if they exist in params
    user_properties = extract_user_properties_from_params(params)

    if length(user_properties) > 0 do
      Map.put(final_form, "user_properties", user_properties)
    else
      final_form
    end
  end

  defp parse_user_properties_from_params(params, current_properties) do
    # Extract user property fields from params and update current properties
    params
    |> Enum.filter(fn {key, _value} ->
      key_str = to_string(key)
      String.starts_with?(key_str, "user_property_")
    end)
    |> Enum.reduce(current_properties, fn {param_key, value}, acc ->
      case extract_index_and_field(to_string(param_key)) do
        {index, field} when index < length(acc) ->
          List.update_at(acc, index, fn property ->
            Map.put(property, field, value)
          end)

        _ ->
          acc
      end
    end)
  end

  defp extract_index_and_field(param_key) do
    # Extract index and field from keys like "user_property_key_0" or "user_property_value_0"
    case String.split(param_key, "_") do
      ["user", "property", field, index_str] ->
        case Integer.parse(index_str) do
          {index, ""} -> {index, field}
          _ -> nil
        end

      _ ->
        nil
    end
  end

  defp extract_user_properties_from_params(params) do
    # Extract user properties from form params
    params
    |> Enum.filter(fn {key, _value} ->
      key_str = to_string(key)
      String.starts_with?(key_str, "user_property_")
    end)
    |> Enum.group_by(fn {key, _value} ->
      # Extract index from key like "user_property_key_0" or "user_property_value_0"
      key_str = to_string(key)

      key_str
      |> String.split("_")
      |> List.last()
      |> String.to_integer()
    end)
    |> Enum.sort_by(fn {index, _} -> index end)
    |> Enum.map(fn {_index, properties} ->
      # Convert list of key-value pairs to a map
      Enum.reduce(properties, %{"key" => "", "value" => ""}, fn {param_key, value}, acc ->
        param_key_str = to_string(param_key)

        if String.contains?(param_key_str, "_key_") do
          Map.put(acc, "key", value)
        else
          Map.put(acc, "value", value)
        end
      end)
    end)
  end

  defp smart_client_selection(form, active_broker_name) do
    connected_clients = get_connected_clients(active_broker_name || "")
    current_client_id = form["client_id"]

    # Check if current client is still valid and connected
    client_still_connected =
      Enum.any?(connected_clients, fn client ->
        client.client_id == current_client_id
      end)

    # If no client selected or current client disconnected, select first available
    if current_client_id == "" || current_client_id == nil || !client_still_connected do
      case connected_clients do
        [first_client | _] ->
          Logger.debug(
            "Smart client selection: selecting #{first_client.client_id} (MQTT #{first_client.mqtt_version || "5.0"})"
          )

          Map.put(form, "client_id", first_client.client_id)

        [] ->
          Logger.debug("Smart client selection: no connected clients available")
          form
      end
    else
      Logger.debug("Smart client selection: keeping current client #{current_client_id}")
      form
    end
  end

  defp get_connected_clients(broker_name) when is_binary(broker_name) and broker_name != "" do
    # Get broker-specific client IDs
    broker_client_ids = get_broker_client_ids(broker_name)

    # Get all connected clients
    all_connected_clients = Mqttable.MqttClient.Manager.get_connected_clients()

    # Filter to only include clients that belong to this broker
    all_connected_clients
    |> Enum.filter(fn client -> client.client_id in broker_client_ids end)
  end

  defp get_connected_clients(_broker_name) do
    # If no broker name provided, return empty list
    []
  end

  defp get_broker_client_ids(broker_name) do
    # Get all connection sets
    connection_sets = Mqttable.ConnectionSets.get_all()

    # Find the broker by name
    broker =
      Enum.find(connection_sets, fn set ->
        Map.get(set, :name) == broker_name
      end)

    case broker do
      nil ->
        []

      broker ->
        # Extract client IDs from connections in this broker
        broker
        |> Map.get(:connections, [])
        |> Enum.map(fn conn -> Map.get(conn, :client_id) end)
        |> Enum.filter(&(&1 != nil && &1 != ""))
        |> Enum.sort()
    end
  end

  defp build_mqtt5_publish_properties(params, connected_clients, client_id) do
    # Check if client supports MQTT 5.0
    client = Enum.find(connected_clients, fn c -> c.client_id == client_id end)

    case client do
      %{mqtt_version: version} when version in ["5.0", "5"] ->
        # Build MQTT 5.0 properties map
        properties = %{}

        # Add content type if provided
        properties =
          if params["content_type"] && params["content_type"] != "" do
            Map.put(properties, :"Content-Type", params["content_type"])
          else
            properties
          end

        # Add payload format indicator
        properties =
          if params["payload_format_indicator"] == "on" do
            Map.put(properties, :"Payload-Format-Indicator", 1)
          else
            properties
          end

        # Add message expiry interval
        properties =
          if params["message_expiry_interval"] && params["message_expiry_interval"] != "" do
            case Integer.parse(params["message_expiry_interval"]) do
              {interval, ""} when interval >= 0 ->
                Map.put(properties, :"Message-Expiry-Interval", interval)

              _ ->
                properties
            end
          else
            properties
          end

        # Add topic alias
        properties =
          if params["topic_alias"] && params["topic_alias"] != "" do
            case Integer.parse(params["topic_alias"]) do
              {alias, ""} when alias >= 0 and alias <= 65535 ->
                # Only add topic alias if it's greater than 0 (0 means no alias)
                if alias > 0 do
                  Map.put(properties, :"Topic-Alias", alias)
                else
                  properties
                end

              _ ->
                properties
            end
          else
            properties
          end

        # Add response topic
        properties =
          if params["response_topic"] && params["response_topic"] != "" do
            Map.put(properties, :"Response-Topic", params["response_topic"])
          else
            properties
          end

        # Add correlation data
        properties =
          if params["correlation_data"] && params["correlation_data"] != "" do
            Map.put(properties, :"Correlation-Data", params["correlation_data"])
          else
            properties
          end

        # Add user properties
        user_properties = extract_user_properties_from_params(params)

        valid_user_properties =
          Enum.filter(user_properties, fn %{"key" => key, "value" => value} ->
            key != "" && value != ""
          end)

        properties =
          if length(valid_user_properties) > 0 do
            user_props_list =
              Enum.map(valid_user_properties, fn %{"key" => key, "value" => value} ->
                {key, value}
              end)

            Map.put(properties, :"User-Property", user_props_list)
          else
            properties
          end

        properties

      _ ->
        # Client doesn't support MQTT 5.0 or not found
        %{}
    end
  end

  defp get_mqtt5_properties_collapsed_state(broker_name) do
    if broker_name do
      ui_state = Mqttable.ConnectionSets.get_ui_state()
      mqtt5_collapsed_states = Map.get(ui_state, :mqtt5_properties_collapsed, %{})
      Map.get(mqtt5_collapsed_states, broker_name, false)
    else
      false
    end
  end

  defp save_mqtt5_properties_collapsed_state(broker_name, collapsed) do
    ui_state = Mqttable.ConnectionSets.get_ui_state()
    mqtt5_collapsed_states = Map.get(ui_state, :mqtt5_properties_collapsed, %{})
    updated_mqtt5_collapsed_states = Map.put(mqtt5_collapsed_states, broker_name, collapsed)

    updated_ui_state =
      Map.put(ui_state, :mqtt5_properties_collapsed, updated_mqtt5_collapsed_states)

    Mqttable.ConnectionSets.update_ui_state(updated_ui_state)
  end

  defp process_payload(payload, broker_name) do
    # New simplified payload processing
    # Check if payload contains template syntax and render if needed
    if has_template_syntax?(payload) do
      # Get broker variables if broker_name is provided
      variables = get_broker_variables(broker_name)

      case Mqttable.Templating.Engine.render(payload, %{}, variables) do
        {:ok, rendered_payload} ->
          {rendered_payload, nil}

        {:error, reason} ->
          {payload, "Template error: #{reason}"}
      end
    else
      # Use payload as-is for plain text
      {payload, nil}
    end
  end

  defp has_template_syntax?(content) when is_binary(content) do
    String.contains?(content, "{{") || String.contains?(content, "{%")
  end

  defp has_template_syntax?(_), do: false

  defp get_broker_variables(broker_name) when is_binary(broker_name) and broker_name != "" do
    # Get all connection sets
    connection_sets = Mqttable.ConnectionSets.get_all()

    # Find the broker by name
    broker =
      Enum.find(connection_sets, fn set ->
        Map.get(set, :name) == broker_name
      end)

    case broker do
      nil ->
        %{}

      broker ->
        # Extract enabled variables and convert to map
        broker
        |> Map.get(:variables, [])
        |> Enum.filter(fn var -> Map.get(var, :enabled, true) end)
        |> Enum.reduce(%{}, fn var, acc ->
          name = Map.get(var, :name)
          value = Map.get(var, :value, "")

          if name && name != "" do
            Map.put(acc, name, value)
          else
            acc
          end
        end)
    end
  end

  defp get_broker_variables(_broker_name) do
    %{}
  end

  # Format publish result based on the type of response
  defp format_publish_result(packet_info) do
    case packet_info do
      # QoS 0 messages return simple integer packet_id (usually 0)
      packet_id when is_integer(packet_id) ->
        {"Message sent successfully (Packet ID: #{packet_id})", :info}

      # QoS 1/2 messages return a map with packet_id and properties
      %{packet_id: packet_id, properties: properties, reason_codes: reason_codes}
      when is_map(properties) and is_list(reason_codes) ->
        # Check if there are any reason codes indicating issues
        case reason_codes do
          [] ->
            # No reason codes, successful
            {"Message sent successfully (Packet ID: #{packet_id})", :info}

          [reason_code | _] when is_integer(reason_code) ->
            # Handle reason codes
            case reason_code do
              # Success codes
              0 ->
                {"Message sent successfully (Packet ID: #{packet_id})", :info}

              # Error codes (16 and above are typically errors)
              code when code >= 16 ->
                reason_name = Map.get(properties, :"Reason-String", "Unknown error")
                reason_text = format_reason_code_name(reason_name)
                {"Message send failed: #{reason_text} (Code: #{code})", :error}

              # Warning codes (informational but not necessarily errors)
              code when code in [1, 2, 3, 4] ->
                # Informational codes
                reason_name = Map.get(properties, :"Reason-String", "Unknown")
                reason_text = format_reason_code_name(reason_name)
                {"Message sent with notice: #{reason_text} (Code: #{code})", :warning}

              code ->
                # Other unknown codes
                reason_name = Map.get(properties, :"Reason-String", "Unknown")
                reason_text = format_reason_code_name(reason_name)
                {"Message sent with unknown status: #{reason_text} (Code: #{code})", :warning}
            end

          [%{reason_code: reason_code, reason_name: reason_name} | _] ->
            # Handle structured reason codes
            case reason_code do
              # Success codes
              0 ->
                {"Message sent successfully (Packet ID: #{packet_id})", :info}

              # Error codes (16 and above are typically errors)
              code when code >= 16 ->
                reason_text = format_reason_code_name(reason_name)
                {"Message send failed: #{reason_text} (Code: #{code})", :error}

              # Warning codes (informational but not necessarily errors)
              code when code in [1, 2, 3, 4] ->
                # Informational codes
                reason_text = format_reason_code_name(reason_name)
                {"Message sent with notice: #{reason_text} (Code: #{code})", :warning}

              code ->
                # Other unknown codes
                reason_text = format_reason_code_name(reason_name)
                {"Message sent with unknown status: #{reason_text} (Code: #{code})", :warning}
            end
        end

      %{
        packet_id: packet_id,
        properties: properties,
        reason_code: reason_code,
        reason_code_name: reason_name
      }
      when is_map(properties) and is_integer(reason_code) ->
        case reason_code do
          # Success codes
          0 ->
            {"Message sent successfully (Packet ID: #{packet_id})", :info}

          # Error codes (16 and above are typically errors)
          code when code >= 16 ->
            reason_text = format_reason_code_name(reason_name)
            {"Message send failed: #{reason_text} (Code: #{code})", :error}

          # Warning codes (informational but not necessarily errors)
          code when code in [1, 2, 3, 4] ->
            # Informational codes
            reason_text = format_reason_code_name(reason_name)
            {"Message sent with notice: #{reason_text} (Code: #{code})", :warning}

          code ->
            # Other unknown codes
            reason_text = format_reason_code_name(reason_name)
            {"Message sent with unknown status: #{reason_text} (Code: #{code})", :warning}
        end

      # Fallback for unexpected format
      other ->
        {"Message sent (Response: #{inspect(other)})", :info}
    end
  end

  # Format reason code name for display
  defp format_reason_code_name(reason_name) when is_atom(reason_name) do
    reason_name
    |> Atom.to_string()
    |> String.replace("_", " ")
    |> String.capitalize()
  end

  defp format_reason_code_name(reason_name) when is_binary(reason_name) do
    reason_name
  end

  defp format_reason_code_name(_), do: "Unknown"

  # Add RFC3339 timestamp to alert message
  defp add_timestamp_to_message(message) do
    timestamp = DateTime.utc_now() |> DateTime.to_iso8601()
    "[#{timestamp}] #{message}"
  end
end
