defmodule MqttableWeb.NewConnectionModalComponent do
  @moduledoc """
  LiveComponent for rendering the new connection modal.
  This component is used to create a new connection within a connection set.
  """
  use MqttableWeb, :live_component
  alias MqttableWeb.ConnectionFormComponent
  alias MqttableWeb.Utils.ConnectionValidation

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <h3 class="text-xl font-semibold mb-4 text-center">New Client</h3>
      <.form
        for={%{}}
        as={:connection}
        phx-submit="save_and_connect_connection"
        phx-change="validate"
        phx-target={@myself}
      >
        <div class="connection-form">
          <ConnectionFormComponent.connection_header connection_set={@connection_set} />

          <div class="space-y-4">
            <!-- General Section (Always visible) -->
            <ConnectionFormComponent.general_section
              edit_connection={@edit_connection}
              connection_set={@connection_set}
              validation_errors={@validation_errors}
            />
            
    <!-- Advanced Section -->
            <ConnectionFormComponent.advanced_section edit_connection={@edit_connection} />
            
    <!-- User Properties Section - Only for MQTT 5.0 -->
            <%= if @edit_connection.mqtt_version == "5.0" do %>
              <ConnectionFormComponent.user_properties_section edit_connection={@edit_connection} />
            <% end %>
            
    <!-- Last Will and Testament Section -->
            <ConnectionFormComponent.lwt_section edit_connection={@edit_connection} />

            <div class="flex justify-between space-x-2 pt-3">
              <div></div>
              <div class="flex space-x-2">
                <button type="button" phx-click="close_modal" class="btn">Cancel</button>
                <button type="submit" class="btn btn-primary">Connect</button>
              </div>
            </div>
          </div>
        </div>
      </.form>
    </div>
    """
  end

  @impl true
  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)
      |> assign(:validation_errors, %{})

    {:ok, socket}
  end

  @impl true
  def handle_event("validate", %{"connection" => connection_params}, socket) do
    # Get existing connections from the current connection set
    existing_connections = Map.get(socket.assigns.connection_set, :connections, [])

    # Validate the connection parameters
    case ConnectionValidation.validate_connection_uniqueness(
           connection_params,
           existing_connections
         ) do
      {:ok, _} ->
        {:noreply, assign(socket, :validation_errors, %{})}

      {:error, errors} ->
        {:noreply, assign(socket, :validation_errors, errors)}
    end
  end

  @impl true
  def handle_event("validate", _params, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event(
        "mqtt_version_changed",
        %{"connection" => %{"mqtt_version" => mqtt_version}},
        socket
      ) do
    # Update the edit_connection in the socket with the new MQTT version
    edit_connection = Map.put(socket.assigns.edit_connection, :mqtt_version, mqtt_version)

    {:noreply, assign(socket, :edit_connection, edit_connection)}
  end

  @impl true
  def handle_event("user_property_changed", params, socket) do
    # Forward the event to the parent LiveView
    send(self(), {:user_property_changed, params})
    {:noreply, socket}
  end

  @impl true
  def handle_event("add_user_property", _params, socket) do
    # Forward the event to the parent LiveView
    send(self(), {:add_user_property})
    {:noreply, socket}
  end

  @impl true
  def handle_event("remove_user_property", params, socket) do
    # Forward the event to the parent LiveView
    send(self(), {:remove_user_property, params})
    {:noreply, socket}
  end

  @impl true
  def handle_event("save_and_connect_connection", %{"connection" => connection_params}, socket) do
    # Send the updated params to the parent LiveView
    send(self(), {:save_and_connect_connection, connection_params})

    {:noreply, socket}
  end
end
