defmodule Mqttable.MqttClient.Topic do
  @moduledoc """
  MQTT topic matching and validation functions converted from Erlang emqx_topic module.
  """

  # Constants
  @max_topic_len 65535
  @share "$share"
  @queue "$queue"
  @share_empty_filter "share_empty_filter"
  @share_empty_group "share_empty_group"
  @share_recursively "share_recursively"
  @share_name_invalid_char "share_name_invalid_char"

  # Type definitions
  @type topic :: binary()
  @type word :: binary() | :"" | :+ | :"#"
  @type words :: [word()]
  @type share :: %{group: binary(), topic: binary()}

  # Guards
  defguard is_multi_level_wildcard_not_last(c, rest)
           when (c == :"#" or c == "#") and rest != []

  defguard is_wildcard(w) when w == :+ or w == :"#"

  @doc """
  Match topic name with filter.
  """
  @spec match(topic() | share() | words(), topic() | words()) :: boolean()
  def match(<<"$", _::binary>>, <<"+", _::binary>>), do: false
  def match(<<"$", _::binary>>, <<"#", _::binary>>), do: false

  def match(name, filter) when is_binary(name) and is_binary(filter) do
    match_words(words(name), words(filter))
  end

  def match(%{group: _, topic: _} = name, filter) do
    match_share(name, filter)
  end

  def match(name, %{group: _, topic: _} = filter) do
    match_share(name, filter)
  end

  def match(name, %{topic: topic}) when is_binary(name) and is_binary(topic) do
    match(name, topic)
  end

  def match(name, filter) when is_binary(name) do
    match_words(words(name), filter)
  end

  def match(name, filter) when is_binary(filter) do
    match_words(name, words(filter))
  end

  def match(name, filter) do
    match_words(name, filter)
  end

  defp match_words([<<"$", _::binary>> | _], [w | _]) when is_wildcard(w), do: false
  defp match_words(name, filter), do: match_tokens(name, filter)

  defp match_tokens([], []), do: true
  defp match_tokens([h | t1], [h | t2]), do: match_tokens(t1, t2)
  defp match_tokens([_h | t1], [:+ | t2]), do: match_tokens(t1, t2)
  defp match_tokens([<<>> | t1], [:"" | t2]), do: match_tokens(t1, t2)
  defp match_tokens(_, [:"#"]), do: true
  defp match_tokens(_, _), do: false

  defp match_share(%{topic: name}, filter) when is_binary(filter) do
    match(words(name), words(filter))
  end

  defp match_share(%{group: group, topic: name}, %{group: group, topic: filter}) do
    match(words(name), words(filter))
  end

  defp match_share(%{group: _, topic: _}, _), do: false

  defp match_share(name, %{topic: filter}) when is_binary(name) do
    match(name, filter)
  end

  @doc """
  Check if topic matches any of the given filters.

  ## Examples

      iex> Topic.match_any("sensors/temperature", ["sensors/+", "devices/#"])
      true

      iex> Topic.match_any("invalid/topic", ["sensors/+", "devices/#"])
      false
  """
  @spec match_any(topic() | words(), [topic() | words()]) :: boolean()
  def match_any(_topic, []), do: false

  def match_any(topic, [filter | rest]) do
    match(topic, filter) or match_any(topic, rest)
  end

  @doc """
  Validate topic name or filter.

  ## Examples

      iex> Topic.validate("sensors/temperature")
      true

      iex> Topic.validate({:filter, "sensors/+"})
      true

      iex> Topic.validate({:name, "sensors/+"})
      ** (RuntimeError) topic_name_error
  """
  @spec validate(topic() | {:name | :filter, topic()}) :: true
  def validate(topic) when is_binary(topic), do: validate(:filter, topic)
  def validate({type, topic}) when type in [:name, :filter], do: validate(type, topic)

  @doc """
  Validate topic with specified type.

  Validates MQTT topic names and filters according to MQTT specification.
  Raises appropriate errors for invalid topics.
  """
  @spec validate(:name | :filter, topic()) :: true
  def validate(_, <<>>) do
    # MQTT-5.0 [MQTT-4.7.3-1]
    raise "empty_topic"
  end

  def validate(_, topic) when is_binary(topic) and byte_size(topic) > @max_topic_len do
    # MQTT-5.0 [MQTT-4.7.3-3]
    raise "topic_too_long"
  end

  def validate(:filter, shared_filter = <<@share, "/", _rest::binary>>) do
    validate_share(shared_filter)
  end

  def validate(:filter, filter) when is_binary(filter) do
    validate2(words(filter))
  end

  def validate(:name, topic) when is_binary(topic) do
    topic_words = words(topic)

    case validate2(topic_words) and not wildcard(topic_words) do
      true -> true
      false -> raise "topic_name_error"
    end
  end

  defp validate2([]), do: true
  defp validate2([:"#"]), do: true

  defp validate2([c | words]) when is_multi_level_wildcard_not_last(c, words) do
    raise "topic_invalid_#"
  end

  defp validate2([:"" | words]), do: validate2(words)
  defp validate2([:+ | words]), do: validate2(words)
  defp validate2([w | words]), do: validate3(w) and validate2(words)

  defp validate3(<<>>), do: true

  defp validate3(<<c::utf8, _rest::binary>>) when c in [?#, ?+, 0] do
    raise "topic_invalid_char"
  end

  defp validate3(<<_::utf8, rest::binary>>), do: validate3(rest)

  defp validate_share(<<@share, "/", rest::binary>>) when rest == <<>> or rest == <<"/">> do
    # MQTT-5.0 [MQTT-4.8.2-1]
    raise @share_empty_filter
  end

  defp validate_share(<<@share, "/", rest::binary>>) do
    case String.split(rest, "/", parts: 2) do
      [<<>>, _] ->
        # MQTT-5.0 [MQTT-4.8.2-1]
        raise @share_empty_group

      [_, <<>>] ->
        # MQTT-5.0 [MQTT-4.7.3-1]
        raise @share_empty_filter

      [share_name, filter] ->
        validate_share(share_name, filter)
    end
  end

  defp validate_share(_share_name, <<@share, "/", _rest::binary>>) do
    raise @share_recursively
  end

  defp validate_share(share_name, filter) do
    case :binary.match(share_name, [<<"+">>, <<"#">>]) do
      :nomatch -> validate2(words(filter))
      _ -> raise @share_name_invalid_char
    end
  end

  @doc """
  Parse topic filter with default options.
  """
  @spec parse(topic() | share() | {topic() | share(), map()}) :: {topic() | share(), map()}
  def parse(topic_filter) when is_binary(topic_filter) or is_map(topic_filter) do
    parse(topic_filter, %{})
  end

  def parse({topic_filter, options}) when is_binary(topic_filter) or is_map(topic_filter) do
    parse(topic_filter, options)
  end

  @doc """
  Parse topic filter with options.
  """
  @spec parse(topic() | share(), map()) :: {topic() | share(), map()}
  def parse(%{topic: <<@queue, "/", _::binary>> = topic}, _options) do
    raise "invalid_topic_filter: #{topic}"
  end

  def parse(%{topic: <<@share, "/", _::binary>> = topic}, _options) do
    raise "invalid_topic_filter: #{topic}"
  end

  def parse(%{group: _, topic: _} = t, %{nl: 1} = _options) do
    # Protocol Error and Should Disconnect
    # MQTT-5.0 [MQTT-3.8.3-4] and [MQTT-4.13.1-1]
    raise "invalid_subopts_nl: #{maybe_format_share(t)}"
  end

  def parse(%{group: group, topic: topic} = _t, options) do
    {%{group: group, topic: topic}, options}
  end

  def parse(<<@queue, "/", topic::binary>>, options) do
    parse(%{group: @queue, topic: topic}, options)
  end

  def parse(topic_filter = <<@share, "/", rest::binary>>, options) do
    case String.split(rest, "/", parts: 2) do
      [_any] ->
        raise "invalid_topic_filter: #{topic_filter}"

      [group, topic] ->
        case :binary.match(group, [<<"+">>, <<"#">>]) do
          :nomatch -> parse(%{group: group, topic: topic}, options)
          _ -> raise "invalid_topic_filter: #{topic_filter}"
        end
    end
  end

  def parse(topic_filter = <<"$exclusive/", topic::binary>>, options) do
    case topic do
      <<>> ->
        raise "invalid_topic_filter: #{topic_filter}"

      _ ->
        {topic, Map.put(options, :is_exclusive, true)}
    end
  end

  def parse(topic_filter, options) when is_binary(topic_filter) do
    {topic_filter, options}
  end

  # Helper functions

  @doc """
  Check if topic contains wildcards.
  """
  @spec wildcard(topic() | share() | words()) :: boolean()
  def wildcard(%{topic: topic}) when is_binary(topic) do
    wildcard(topic)
  end

  def wildcard(topic) when is_binary(topic) do
    wildcard(words(topic))
  end

  def wildcard([]), do: false
  def wildcard([:"#" | _]), do: true
  def wildcard([:+ | _]), do: true
  def wildcard([_h | t]), do: wildcard(t)

  @doc """
  Split topic to words.

  ## Examples

      iex> Topic.words("sensors/temperature/room1")
      ["sensors", "temperature", "room1"]

      iex> Topic.words("sensors/+/temperature")
      ["sensors", :+, "temperature"]
  """
  @spec words(topic() | share()) :: words()
  def words(%{topic: topic}) when is_binary(topic), do: words(topic)

  def words(topic) when is_binary(topic) do
    topic
    |> String.split("/")
    |> Enum.map(&word/1)
  end

  defp word(""), do: :""
  defp word("+"), do: :+
  defp word("#"), do: :"#"
  defp word(bin), do: bin

  @doc """
  Join words back to topic.
  """
  @spec join([word()]) :: binary()
  def join([]), do: <<>>

  def join([word | words]) do
    do_join(bin(word), words)
  end

  defp do_join(topic_acc, []), do: topic_acc

  defp do_join(_topic_acc, [c | words]) when is_multi_level_wildcard_not_last(c, words) do
    raise "topic_invalid_#"
  end

  defp do_join(topic_acc, [word | words]) do
    do_join(<<topic_acc::binary, "/", bin(word)::binary>>, words)
  end

  defp bin(:""), do: <<>>
  defp bin(:+), do: <<"+">>
  defp bin(:"#"), do: <<"#">>
  defp bin(b) when is_binary(b), do: b
  defp bin(l) when is_list(l), do: List.to_string(l)

  defp maybe_format_share(%{group: @queue, topic: topic}) do
    join([@queue, topic])
  end

  defp maybe_format_share(%{group: group, topic: topic}) do
    join([@share, group, topic])
  end

  defp maybe_format_share(topic) do
    join([topic])
  end
end
