defmodule Mqttable.MqttClient.EtsOperations do
  @moduledoc """
  Centralized ETS operations for MQTT client management.

  This module provides a clean interface for all ETS table operations
  related to MQTT client records. It encapsulates the ETS table structure
  and provides type-safe operations for client record management.
  """

  # Type definitions
  @type client_id :: String.t()
  @type worker_pid :: pid()
  @type client_pid :: pid() | nil
  @type mqtt_opts :: keyword() | nil
  @type parse_state :: term()
  @type client_status :: :connected | :disconnected | :reconnecting | :connecting
  @type client_record ::
          {client_id(), worker_pid(), client_pid(), mqtt_opts(), parse_state(), client_status()}

  # ETS table name
  @table_name Mqttable.MqttClient.Manager

  @doc """
  Stores a client record in the ETS table.

  ## Parameters

  - `client_id`: Unique identifier for the client
  - `worker_pid`: PID of the worker process
  - `client_pid`: PID of the MQTT client process (can be nil)
  - `mqtt_opts`: MQTT connection options
  - `status`: Current connection status

  ## Examples

      iex> EtsOperations.store_client_record("client1", self(), nil, [], :connecting)
      true
  """
  @spec store_client_record(client_id(), worker_pid(), client_pid(), mqtt_opts(), client_status()) ::
          boolean()
  def store_client_record(client_id, worker_pid, client_pid, mqtt_opts, status) do
    opts = mqtt_opts || []
    parse_state = build_parse_state(opts)

    record = {client_id, worker_pid, client_pid, opts, parse_state, status}
    :ets.insert(@table_name, record)
  end

  @doc """
  Removes a client record from the ETS table.
  """
  @spec remove_client_record(client_id()) :: boolean()
  def remove_client_record(client_id) do
    :ets.delete(@table_name, client_id)
  end

  @doc """
  Looks up the status of a client.
  Returns :disconnected if the client is not found.
  """
  @spec lookup_client_status(client_id()) :: client_status()
  def lookup_client_status(client_id) do
    case :ets.lookup(@table_name, client_id) do
      [{^client_id, _worker_pid, _client_pid, _mqtt_opts, _parse_state, status}] ->
        status

      [] ->
        :disconnected
    end
  end

  @doc """
  Looks up the parse state for a client.
  Returns nil if the client is not found.
  """
  @spec lookup_client_parse_state(client_id()) :: parse_state() | nil
  def lookup_client_parse_state(client_id) do
    case :ets.lookup(@table_name, client_id) do
      [{^client_id, _worker_pid, _client_pid, _mqtt_opts, parse_state, _status}] ->
        parse_state

      [] ->
        nil
    end
  end

  @doc """
  Updates the parse state for a client.
  """
  @spec store_client_parse_state(client_id(), parse_state()) :: boolean()
  def store_client_parse_state(client_id, parse_state) do
    :ets.update_element(@table_name, client_id, {5, parse_state})
  end

  @doc """
  Looks up a complete client record.
  Returns a tuple with status, worker_pid, client_pid, and mqtt_opts.
  """
  @spec lookup_client_record(client_id()) ::
          {client_status(), worker_pid() | nil, client_pid(), mqtt_opts()}
  def lookup_client_record(client_id) do
    case :ets.lookup(@table_name, client_id) do
      [{^client_id, worker_pid, client_pid, mqtt_opts, _parse_state, status}] ->
        {status, worker_pid, client_pid, mqtt_opts}

      [] ->
        {:disconnected, nil, nil, nil}
    end
  end

  @doc """
  Gets all client records from the ETS table.
  """
  @spec get_all_client_records() :: [client_record()]
  def get_all_client_records do
    :ets.tab2list(@table_name)
  end

  @doc """
  Gets all active clients as a map of client_id to worker_pid.
  """
  @spec get_all_clients() :: %{client_id() => worker_pid()}
  def get_all_clients do
    get_all_client_records()
    |> Enum.map(fn {client_id, worker_pid, _client_pid, _mqtt_opts, _parse_state, _status} ->
      {client_id, worker_pid}
    end)
    |> Enum.into(%{})
  end

  @doc """
  Gets all connected clients with their information.
  """
  @spec get_connected_clients() :: [
          %{client_id: client_id(), status: client_status(), mqtt_version: String.t()}
        ]
  def get_connected_clients do
    get_all_client_records()
    |> Enum.filter(fn {_client_id, _worker_pid, _client_pid, _mqtt_opts, _parse_state, status} ->
      status == :connected
    end)
    |> Enum.map(fn {client_id, _worker_pid, _client_pid, mqtt_opts, _parse_state, status} ->
      mqtt_version = extract_mqtt_version_from_opts(mqtt_opts)
      %{client_id: client_id, status: status, mqtt_version: mqtt_version}
    end)
  end

  @doc """
  Updates the client PID and status for an existing record.
  """
  @spec update_client_connection(client_id(), client_pid(), mqtt_opts(), client_status()) ::
          boolean()
  def update_client_connection(client_id, client_pid, mqtt_opts, status) do
    opts = mqtt_opts || []
    parse_state = build_parse_state(opts)

    updates = [
      # client_pid position
      {3, client_pid},
      # mqtt_opts position
      {4, opts},
      # parse_state position
      {5, parse_state},
      # status position
      {6, status}
    ]

    :ets.update_element(@table_name, client_id, updates)
  end

  @doc """
  Updates only the status of a client record.
  """
  @spec update_client_status(client_id(), client_status()) :: boolean()
  def update_client_status(client_id, status) do
    :ets.update_element(@table_name, client_id, {6, status})
  end

  @doc """
  Checks if a client record exists in the ETS table.
  """
  @spec client_exists?(client_id()) :: boolean()
  def client_exists?(client_id) do
    case :ets.lookup(@table_name, client_id) do
      [] -> false
      _ -> true
    end
  end

  # Private helper functions

  @spec build_parse_state(mqtt_opts()) :: parse_state()
  defp build_parse_state(opts) do
    max_size = Map.get(Keyword.get(opts, :properties, %{}), :"Receive-Maximum", 0xFFFFFFF)

    version =
      case Keyword.get(opts, :proto_ver, :v5) do
        :v5 -> 5
        _ -> 4
      end

    :emqtt_frame.initial_parse_state(%{max_size: max_size, version: version})
  end

  @spec extract_mqtt_version_from_opts(mqtt_opts()) :: String.t()
  defp extract_mqtt_version_from_opts(nil), do: "5.0"

  defp extract_mqtt_version_from_opts(mqtt_opts) when is_list(mqtt_opts) do
    case Keyword.get(mqtt_opts, :proto_ver, :v5) do
      :v5 -> "5.0"
      :v4 -> "3.1.1"
      :v3 -> "3.1"
      _ -> "5.0"
    end
  end

  defp extract_mqtt_version_from_opts(_), do: "5.0"
end
