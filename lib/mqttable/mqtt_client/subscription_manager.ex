defmodule Mqttable.MqttClient.SubscriptionManager do
  @moduledoc """
  Pure functions for managing MQTT topic subscriptions and resubscriptions.

  This module provides composable functions for handling topic subscriptions,
  validation, and resubscription logic during client reconnections. All functions
  are pure and side-effect free, focusing on data transformation and validation.
  """

  require Logger
  alias Mqttable.MqttClient.Topic

  # Type definitions
  @type client_id :: String.t()
  @type client_pid :: pid()
  @type topic :: String.t()
  @type subscription_opts :: keyword()
  @type subscription_properties :: map()
  @type reason_codes :: [integer()]
  @type subscription_result :: {:ok, map(), reason_codes()} | {:error, atom(), String.t()}

  @doc """
  Validates a topic filter before subscription.

  ## Examples

      iex> SubscriptionManager.validate_topic_filter("sensors/+/temperature")
      :ok

      iex> SubscriptionManager.validate_topic_filter("invalid#topic")
      {:error, "Invalid topic filter: topic_invalid_#"}
  """
  @spec validate_topic_filter(topic()) :: :ok | {:error, String.t()}
  def validate_topic_filter(topic) do
    try do
      Topic.validate({:filter, topic})
      :ok
    rescue
      e in RuntimeError ->
        error_message = "Invalid topic filter: #{e.message}"
        {:error, error_message}
    catch
      :error, reason ->
        error_message = "Invalid topic filter: #{inspect(reason)}"
        {:error, error_message}
    end
  end

  @doc """
  Processes subscription options to convert nl and rap values from 0/1 to false/true.
  """
  @spec process_subscription_options(subscription_opts()) :: subscription_opts()
  def process_subscription_options(options) do
    Enum.map(options, fn
      {key, value} when key in [:nl, :rap] ->
        converted_value =
          case value do
            0 -> false
            1 -> true
            _ -> value
          end

        {key, converted_value}

      option ->
        option
    end)
  end

  @doc """
  Prepares subscription properties with optional subscription identifier.
  """
  @spec prepare_subscription_properties(integer() | nil) :: subscription_properties()
  def prepare_subscription_properties(sub_id) when is_integer(sub_id) and sub_id > 0 do
    %{:"Subscription-Identifier" => sub_id}
  end

  def prepare_subscription_properties(_), do: %{}

  @doc """
  Checks if subscription was successful based on reason codes.
  """
  @spec check_subscription_success(reason_codes()) :: :ok | {:error, String.t()}
  def check_subscription_success(reason_codes) do
    Enum.reduce_while(reason_codes, :ok, fn reason_code, _acc ->
      case reason_code do
        # MQTT v3.1.1 and v5.0 success codes (0x00, 0x01, 0x02)
        code when code in [0, 1, 2] ->
          {:cont, :ok}

        # MQTT v5.0 and v3.1.1 error codes
        0x80 ->
          {:halt, {:error, "Subscription failed: Unspecified error"}}

        0x83 ->
          {:halt, {:error, "Implementation specific error"}}

        0x87 ->
          {:halt, {:error, "Not authorized"}}

        0x8F ->
          {:halt, {:error, "Topic filter invalid"}}

        0x91 ->
          {:halt, {:error, "Packet identifier in use"}}

        0x97 ->
          {:halt, {:error, "Quota exceeded"}}

        0x9E ->
          {:halt, {:error, "Shared subscriptions not supported"}}

        0xA1 ->
          {:halt, {:error, "Subscription identifiers not supported"}}

        0xA2 ->
          {:halt, {:error, "Wildcard subscriptions not supported"}}

        # Unknown error codes
        _ ->
          {:halt, {:error, "Subscription failed with reason code: #{reason_code}"}}
      end
    end)
  end

  @doc """
  Builds subscription options and properties from a topic map.
  """
  @spec build_subscription_options(map()) :: {subscription_opts(), subscription_properties()}
  def build_subscription_options(topic_map) do
    # Build subscription options
    sub_opts = [
      {:qos, Map.get(topic_map, :qos, 0)}
    ]

    # Add optional parameters if they exist
    sub_opts =
      sub_opts
      |> add_option_if_exists(topic_map, :nl)
      |> add_option_if_exists(topic_map, :rap)
      |> add_option_if_exists(topic_map, :rh)
      |> process_subscription_options()

    # Get subscription identifier and prepare properties
    sub_id = Map.get(topic_map, :id)
    props = prepare_subscription_properties(sub_id)

    {sub_opts, props}
  end

  @doc """
  Processes saved topics for resubscription during client reconnection.
  Returns a list of processed topic entries ready for subscription.
  """
  @spec process_saved_topics_for_resubscription([term()]) :: [
          {topic(), {subscription_opts(), subscription_properties()}}
        ]
  def process_saved_topics_for_resubscription(topics) do
    topics
    |> Enum.map(&normalize_topic_entry/1)
    |> Enum.filter(&valid_topic_entry?/1)
    |> Enum.map(&extract_topic_and_options/1)
  end

  @doc """
  Validates and prepares a single topic for subscription.
  """
  @spec prepare_topic_for_subscription(topic(), subscription_opts(), subscription_properties()) ::
          {:ok, {topic(), subscription_opts(), subscription_properties()}} | {:error, String.t()}
  def prepare_topic_for_subscription(topic, sub_opts, props) do
    case validate_topic_filter(topic) do
      :ok ->
        processed_opts = process_subscription_options(sub_opts)
        {:ok, {topic, processed_opts, props}}

      {:error, error_message} ->
        {:error, error_message}
    end
  end

  # Private helper functions

  @spec add_option_if_exists(subscription_opts(), map(), atom()) :: subscription_opts()
  defp add_option_if_exists(opts, map, key) do
    if Map.has_key?(map, key) do
      value = Map.get(map, key)
      [{key, value} | opts]
    else
      opts
    end
  end

  @spec normalize_topic_entry(term()) :: map() | nil
  defp normalize_topic_entry(topic_entry) do
    case topic_entry do
      # Handle string format (old format)
      topic when is_binary(topic) ->
        %{topic: topic, qos: 0}

      # Handle map format with atom keys (new format)
      %{topic: topic_str} = topic_map when is_map(topic_map) and is_binary(topic_str) ->
        topic_map

      # Handle map format with string keys (legacy format from JSON)
      topic_map when is_map(topic_map) and map_size(topic_map) > 0 ->
        topic_str = Map.get(topic_map, :topic) || Map.get(topic_map, "topic")

        if topic_str && is_binary(topic_str) do
          # Convert string keys to atom keys if needed
          if Map.has_key?(topic_map, "topic") do
            for {key, val} <- topic_map, into: %{}, do: {String.to_atom(key), val}
          else
            topic_map
          end
        else
          nil
        end

      # Invalid entries
      _ ->
        nil
    end
  end

  @spec valid_topic_entry?(map() | nil) :: boolean()
  defp valid_topic_entry?(nil), do: false
  defp valid_topic_entry?(%{topic: topic}) when is_binary(topic) and topic != "", do: true
  defp valid_topic_entry?(_), do: false

  @spec extract_topic_and_options(map()) ::
          {topic(), {subscription_opts(), subscription_properties()}}
  defp extract_topic_and_options(%{topic: topic} = topic_map) do
    {sub_opts, props} = build_subscription_options(topic_map)
    {topic, {sub_opts, props}}
  end

  @doc """
  Logs resubscription attempts and results for debugging.
  """
  @spec log_resubscription_attempt(client_id(), topic(), :ok | :error) :: :ok
  def log_resubscription_attempt(client_id, topic, result) do
    case result do
      :ok ->
        Logger.info("Successfully resubscribed client #{client_id} to topic #{topic}")

      :error ->
        Logger.error("Failed to resubscribe client #{client_id} to topic #{topic}")
    end
  end

  @doc """
  Logs topic validation failures during resubscription.
  """
  @spec log_topic_validation_failure(topic(), String.t()) :: :ok
  def log_topic_validation_failure(topic, error_message) do
    Logger.error("Topic validation failed for resubscription to #{topic}: #{error_message}")
  end
end
