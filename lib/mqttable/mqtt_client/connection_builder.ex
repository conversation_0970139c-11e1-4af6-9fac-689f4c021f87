defmodule Mqttable.MqttClient.ConnectionBuilder do
  @moduledoc """
  Pure functions for building MQTT connection options.

  This module provides composable functions for constructing MQTT client
  configuration options from connection and broker parameters. All functions
  are pure and side-effect free, making them easily testable and composable.
  """

  alias MqttableWeb.Utils.ConnectionHelpers

  # Type definitions
  @type connection :: map()
  @type broker :: map()
  @type mqtt_opts :: keyword()
  @type mqtt_properties :: map()

  @doc """
  Builds complete MQTT connection options from connection and broker parameters.

  ## Examples

      iex> connection = %{client_id: "test", mqtt_version: "5.0"}
      iex> broker = %{host: "localhost", port: "1883", protocol: "mqtt"}
      iex> opts = ConnectionBuilder.build_mqtt_options(connection, broker)
      iex> Keyword.get(opts, :clientid)
      "test"
  """
  @spec build_mqtt_options(connection(), broker()) :: mqtt_opts()
  def build_mqtt_options(connection, broker) do
    connection
    |> ConnectionHelpers.prepare_connection_for_mqtt()
    |> build_base_options()
    |> add_authentication_options(connection)
    |> add_protocol_options(broker)
    |> add_mqtt_version_options(connection)
  end

  @doc """
  Builds base MQTT connection options.
  """
  @spec build_base_options(connection()) :: mqtt_opts()
  def build_base_options(connection) do
    [
      clientid: connection.client_id,
      clean_start: connection.clean_start,
      keepalive: connection.keep_alive,
      proto_ver: mqtt_version_to_proto_ver(connection.mqtt_version)
    ]
  end

  @doc """
  Adds authentication options to the base options.
  """
  @spec add_authentication_options(mqtt_opts(), connection()) :: mqtt_opts()
  def add_authentication_options(base_opts, connection) do
    base_opts
    |> add_username(connection)
    |> add_password(connection)
    |> add_will_message(connection)
  end

  @doc """
  Adds protocol-specific options based on broker configuration.
  """
  @spec add_protocol_options(mqtt_opts(), broker()) :: mqtt_opts()
  def add_protocol_options(opts, broker) do
    opts
    |> add_host_and_port(broker)
    |> add_transport_options(broker)
  end

  @doc """
  Adds MQTT version-specific options and properties.
  """
  @spec add_mqtt_version_options(mqtt_opts(), connection()) :: mqtt_opts()
  def add_mqtt_version_options(opts, connection) do
    case connection.mqtt_version do
      "5.0" ->
        properties = build_mqtt5_properties(connection)

        if map_size(properties) > 0 do
          [{:properties, properties} | opts]
        else
          opts
        end

      _ ->
        opts
    end
  end

  # Private helper functions

  @spec mqtt_version_to_proto_ver(String.t()) :: atom()
  defp mqtt_version_to_proto_ver(mqtt_version) do
    case mqtt_version do
      "5.0" -> :v5
      "3.1.1" -> :v4
      "3.1" -> :v3
      _ -> :v4
    end
  end

  @spec add_host_and_port(mqtt_opts(), broker()) :: mqtt_opts()
  defp add_host_and_port(opts, broker) do
    [
      {:host, String.to_charlist(broker.host)},
      {:port, String.to_integer(broker.port)}
      | opts
    ]
  end

  @spec add_transport_options(mqtt_opts(), broker()) :: mqtt_opts()
  defp add_transport_options(opts, broker) do
    case broker.protocol do
      "mqtts" ->
        [{:ssl, true} | opts]

      "wss" ->
        ws_path = Map.get(broker, :ws_path, "/mqtt")
        [{:ssl, true}, {:ws_path, ws_path} | opts]

      "ws" ->
        ws_path = Map.get(broker, :ws_path, "/mqtt")
        [{:ws_path, ws_path} | opts]

      _ ->
        opts
    end
  end

  @spec add_username(mqtt_opts(), connection()) :: mqtt_opts()
  defp add_username(opts, connection) do
    if connection.username && connection.username != "" do
      [{:username, connection.username} | opts]
    else
      opts
    end
  end

  @spec add_password(mqtt_opts(), connection()) :: mqtt_opts()
  defp add_password(opts, connection) do
    if connection.password && connection.password != "" do
      [{:password, connection.password} | opts]
    else
      opts
    end
  end

  @spec add_will_message(mqtt_opts(), connection()) :: mqtt_opts()
  defp add_will_message(opts, connection) do
    if connection.will_topic && connection.will_topic != "" do
      build_will_options(opts, connection)
    else
      opts
    end
  end

  @spec build_will_options(mqtt_opts(), connection()) :: mqtt_opts()
  defp build_will_options(opts, connection) do
    will_opts = [
      will_topic: connection.will_topic,
      will_payload: connection.will_payload || "",
      will_qos: String.to_integer(connection.will_qos || "0"),
      will_retain: connection.will_retain
    ]

    opts = Keyword.merge(opts, will_opts)

    if connection.mqtt_version == "5.0" do
      add_mqtt5_will_properties(opts, connection)
    else
      opts
    end
  end

  @spec add_mqtt5_will_properties(mqtt_opts(), connection()) :: mqtt_opts()
  defp add_mqtt5_will_properties(opts, connection) do
    will_props = build_mqtt5_will_properties(connection)

    if map_size(will_props) > 0 do
      [{:will_props, will_props} | opts]
    else
      opts
    end
  end

  @doc """
  Builds MQTT 5.0 connection properties from connection parameters.
  """
  @spec build_mqtt5_properties(connection()) :: mqtt_properties()
  def build_mqtt5_properties(connection) do
    %{}
    |> add_session_expiry_interval(connection)
    |> add_receive_maximum(connection)
    |> add_maximum_packet_size(connection)
    |> add_topic_alias_maximum(connection)
    |> add_request_response_info(connection)
    |> add_request_problem_info(connection)
    |> add_user_properties(connection)
  end

  @doc """
  Builds MQTT 5.0 will message properties from connection parameters.
  """
  @spec build_mqtt5_will_properties(connection()) :: mqtt_properties()
  def build_mqtt5_will_properties(connection) do
    %{}
    |> add_will_delay_interval(connection)
    |> add_payload_format_indicator(connection)
    |> add_message_expiry_interval(connection)
    |> add_content_type(connection)
    |> add_response_topic(connection)
    |> add_correlation_data(connection)
  end

  # MQTT 5.0 connection properties helpers

  @spec add_session_expiry_interval(mqtt_properties(), connection()) :: mqtt_properties()
  defp add_session_expiry_interval(props, connection) do
    if is_integer(connection.session_expiry_interval) && connection.session_expiry_interval > 0 do
      Map.put(props, :"Session-Expiry-Interval", connection.session_expiry_interval)
    else
      props
    end
  end

  @spec add_receive_maximum(mqtt_properties(), connection()) :: mqtt_properties()
  defp add_receive_maximum(props, connection) do
    if is_integer(connection.receive_maximum) && connection.receive_maximum > 0 do
      Map.put(props, :"Receive-Maximum", connection.receive_maximum)
    else
      props
    end
  end

  @spec add_maximum_packet_size(mqtt_properties(), connection()) :: mqtt_properties()
  defp add_maximum_packet_size(props, connection) do
    if is_integer(connection.maximum_packet_size) && connection.maximum_packet_size > 0 do
      Map.put(props, :"Maximum-Packet-Size", connection.maximum_packet_size)
    else
      props
    end
  end

  @spec add_topic_alias_maximum(mqtt_properties(), connection()) :: mqtt_properties()
  defp add_topic_alias_maximum(props, connection) do
    if is_integer(connection.topic_alias_maximum) && connection.topic_alias_maximum > 0 do
      Map.put(props, :"Topic-Alias-Maximum", connection.topic_alias_maximum)
    else
      props
    end
  end

  @spec add_request_response_info(mqtt_properties(), connection()) :: mqtt_properties()
  defp add_request_response_info(props, connection) do
    if connection.request_response_info do
      Map.put(props, :"Request-Response-Information", 1)
    else
      props
    end
  end

  @spec add_request_problem_info(mqtt_properties(), connection()) :: mqtt_properties()
  defp add_request_problem_info(props, connection) do
    if connection.request_problem_info do
      Map.put(props, :"Request-Problem-Information", 1)
    else
      props
    end
  end

  @spec add_user_properties(mqtt_properties(), connection()) :: mqtt_properties()
  defp add_user_properties(props, connection) do
    if connection.user_properties && is_list(connection.user_properties) &&
         length(connection.user_properties) > 0 do
      valid_props =
        connection.user_properties
        |> Enum.filter(&valid_user_property?/1)
        |> Enum.map(fn prop -> {prop.key, prop.value} end)

      if length(valid_props) > 0 do
        Map.put(props, :"User-Property", valid_props)
      else
        props
      end
    else
      props
    end
  end

  @spec valid_user_property?(map()) :: boolean()
  defp valid_user_property?(prop) do
    is_map(prop) && Map.has_key?(prop, :key) && Map.has_key?(prop, :value) &&
      prop.key != "" && prop.value != ""
  end

  # MQTT 5.0 will properties helpers

  @spec add_will_delay_interval(mqtt_properties(), connection()) :: mqtt_properties()
  defp add_will_delay_interval(props, connection) do
    if is_integer(connection.will_delay_interval) && connection.will_delay_interval > 0 do
      Map.put(props, :"Will-Delay-Interval", connection.will_delay_interval)
    else
      props
    end
  end

  @spec add_payload_format_indicator(mqtt_properties(), connection()) :: mqtt_properties()
  defp add_payload_format_indicator(props, connection) do
    if connection.will_payload_format do
      Map.put(props, :"Payload-Format-Indicator", 1)
    else
      props
    end
  end

  @spec add_message_expiry_interval(mqtt_properties(), connection()) :: mqtt_properties()
  defp add_message_expiry_interval(props, connection) do
    if is_integer(connection.will_message_expiry) && connection.will_message_expiry > 0 do
      Map.put(props, :"Message-Expiry-Interval", connection.will_message_expiry)
    else
      props
    end
  end

  @spec add_content_type(mqtt_properties(), connection()) :: mqtt_properties()
  defp add_content_type(props, connection) do
    if connection.will_content_type && connection.will_content_type != "" do
      Map.put(props, :"Content-Type", connection.will_content_type)
    else
      props
    end
  end

  @spec add_response_topic(mqtt_properties(), connection()) :: mqtt_properties()
  defp add_response_topic(props, connection) do
    if connection.will_response_topic && connection.will_response_topic != "" do
      Map.put(props, :"Response-Topic", connection.will_response_topic)
    else
      props
    end
  end

  @spec add_correlation_data(mqtt_properties(), connection()) :: mqtt_properties()
  defp add_correlation_data(props, connection) do
    if connection.will_correlation_data && connection.will_correlation_data != "" do
      Map.put(props, :"Correlation-Data", connection.will_correlation_data)
    else
      props
    end
  end
end
