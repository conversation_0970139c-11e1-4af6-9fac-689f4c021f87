defmodule Mqttable.Settings do
  @moduledoc """
  API module for managing application settings.
  Provides functions to get, update, and save settings.
  Also provides subscription functionality for real-time updates.
  """

  alias Mqttable.Settings.{Server, Storage}
  alias Phoenix.PubSub

  @pubsub_topic "settings"

  @doc """
  Gets all settings.

  ## Returns

  - Map of all settings
  """
  def get_all do
    Server.get_all()
  end

  @doc """
  Gets a specific setting value.

  ## Parameters

  - `key`: Atom key of the setting to retrieve

  ## Returns

  - The setting value, or nil if not found
  """
  def get(key) when is_atom(key) do
    Server.get(key)
  end

  @doc """
  Gets a setting value with a fallback.

  ## Parameters

  - `key`: Atom key of the setting to retrieve
  - `default`: Default value to return if setting is not found

  ## Returns

  - The setting value, or the default if not found
  """
  def get(key, default) when is_atom(key) do
    case Server.get(key) do
      nil -> default
      value -> value
    end
  end

  @doc """
  Updates multiple settings.

  ## Parameters

  - `settings`: Map of settings to update

  ## Returns

  - `:ok`: Always returns `:ok`
  """
  def update(settings) when is_map(settings) do
    Server.update(settings)
    :ok
  end

  @doc """
  Updates a specific setting.

  ## Parameters

  - `key`: Atom key of the setting to update
  - `value`: New value for the setting

  ## Returns

  - `:ok`: Always returns `:ok`
  """
  def put(key, value) when is_atom(key) do
    Server.put(key, value)
    :ok
  end

  @doc """
  Subscribe to settings updates.
  The subscriber will receive a message in the format:
  {:settings_updated, settings}

  ## Returns

  - `:ok`: Always returns `:ok`
  """
  def subscribe do
    PubSub.subscribe(Mqttable.PubSub, @pubsub_topic)
  end

  @doc """
  Unsubscribe from settings updates.

  ## Returns

  - `:ok`: Always returns `:ok`
  """
  def unsubscribe do
    PubSub.unsubscribe(Mqttable.PubSub, @pubsub_topic)
  end

  @doc """
  Gets the configured time zone.
  Falls back to application config if not set in user settings.

  ## Returns

  - String time zone identifier
  """
  def get_time_zone do
    get(:time_zone, Application.get_env(:mqttable, :time_zone, "UTC"))
  end

  @doc """
  Gets the configured maximum messages per broker.
  Falls back to default value if not set in user settings.

  ## Returns

  - Integer maximum messages per broker
  """
  def get_max_messages_per_broker do
    get(:max_messages_per_broker, 3000)
  end

  @doc """
  Gets the default settings.

  ## Returns

  - Map of default settings
  """
  def default_settings do
    Storage.default_settings()
  end

  @doc """
  Gets a list of common time zones for the settings UI.

  ## Returns

  - List of maps with :value and :label keys
  """
  def get_time_zone_options do
    [
      %{value: "UTC", label: "UTC"},
      %{value: "America/New_York", label: "Eastern Time (US & Canada)"},
      %{value: "America/Chicago", label: "Central Time (US & Canada)"},
      %{value: "America/Denver", label: "Mountain Time (US & Canada)"},
      %{value: "America/Los_Angeles", label: "Pacific Time (US & Canada)"},
      %{value: "Europe/London", label: "London"},
      %{value: "Europe/Paris", label: "Paris"},
      %{value: "Europe/Berlin", label: "Berlin"},
      %{value: "Europe/Rome", label: "Rome"},
      %{value: "Europe/Madrid", label: "Madrid"},
      %{value: "Asia/Tokyo", label: "Tokyo"},
      %{value: "Asia/Shanghai", label: "Shanghai"},
      %{value: "Asia/Hong_Kong", label: "Hong Kong"},
      %{value: "Asia/Singapore", label: "Singapore"},
      %{value: "Asia/Seoul", label: "Seoul"},
      %{value: "Asia/Kolkata", label: "Mumbai, Kolkata, New Delhi"},
      %{value: "Australia/Sydney", label: "Sydney"},
      %{value: "Australia/Melbourne", label: "Melbourne"},
      %{value: "Pacific/Auckland", label: "Auckland"}
    ]
  end
end
