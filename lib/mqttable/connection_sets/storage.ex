defmodule Mqttable.ConnectionSets.Storage do
  @moduledoc """
  Module for handling the persistence of connection sets to disk.
  """

  require Logger

  @data_dir "priv/data"
  @connection_sets_file Path.join(@data_dir, "brokers.json")
  @ui_state_file Path.join(@data_dir, "ui_state.json")

  @doc """
  Saves connection sets and UI state to JSON files.

  ## Parameters

  - `connection_sets`: List of connection set maps to be saved
  - `ui_state`: Map containing UI state information

  ## Returns

  - `{:ok, path}`: On successful save, returns the path where the files were saved
  - `{:error, reason}`: On failure, returns the error reason
  """
  def save(connection_sets, ui_state \\ %{expanded_sets: %{}})
      when is_list(connection_sets) and is_map(ui_state) do
    # Ensure the data directory exists
    File.mkdir_p!(@data_dir)

    # Save connection sets
    connection_sets_result = save_connection_sets(connection_sets)

    # Save UI state
    ui_state_result = save_ui_state(ui_state)

    case {connection_sets_result, ui_state_result} do
      {{:ok, _}, {:ok, _}} -> {:ok, @data_dir}
      {{:error, reason}, _} -> {:error, reason}
      {_, {:error, reason}} -> {:error, reason}
    end
  end

  @doc """
  Loads connection sets and UI state from JSON files.

  ## Returns

  - `{:ok, connection_sets, ui_state}`: On successful load, returns the list of connection sets and UI state
  - `{:error, reason}`: On failure, returns the error reason
  """
  def load do
    # Load connection sets
    connection_sets_result = load_connection_sets()

    # Load UI state
    ui_state_result = load_ui_state()

    case {connection_sets_result, ui_state_result} do
      {{:ok, connection_sets}, {:ok, ui_state}} ->
        {:ok, connection_sets, ui_state}

      {{:error, reason}, _} ->
        {:error, reason}

      {_, {:error, reason}} ->
        {:error, reason}
    end
  end

  # Private function to save connection sets
  defp save_connection_sets(connection_sets) do
    # Convert connection sets to JSON
    case Jason.encode(connection_sets, pretty: true) do
      {:ok, json} ->
        # Write JSON to file
        case File.write(@connection_sets_file, json) do
          :ok -> {:ok, @connection_sets_file}
          {:error, reason} -> {:error, "Failed to write connection sets file: #{reason}"}
        end

      {:error, reason} ->
        {:error, "Failed to encode connection sets to JSON: #{inspect(reason)}"}
    end
  end

  # Private function to save UI state
  defp save_ui_state(ui_state) do
    # Normalize broker name keys to strings for consistency across all UI state fields
    normalized_ui_state =
      ui_state
      |> normalize_broker_name_keys(:expanded_sets)
      |> normalize_broker_name_keys(:send_modal_forms)
      |> normalize_broker_name_keys(:mqtt5_properties_collapsed)
      |> normalize_broker_name_keys(:trace_filters)

    # Convert UI state to JSON
    case Jason.encode(normalized_ui_state, pretty: true) do
      {:ok, json} ->
        # Write JSON to file
        case File.write(@ui_state_file, json) do
          :ok -> {:ok, @ui_state_file}
          {:error, reason} -> {:error, "Failed to write UI state file: #{reason}"}
        end

      {:error, reason} ->
        {:error, "Failed to encode UI state to JSON: #{inspect(reason)}"}
    end
  end

  # Private function to load connection sets
  defp load_connection_sets do
    if File.exists?(@connection_sets_file) do
      case File.read(@connection_sets_file) do
        {:ok, json} ->
          case Jason.decode(json) do
            {:ok, connection_sets} ->
              # Convert the connection sets to maps with atom keys
              connection_sets =
                Enum.map(connection_sets, fn set ->
                  # Convert string keys to atoms at the top level
                  set = for {key, val} <- set, into: %{}, do: {String.to_atom(key), val}

                  # Convert variables list items to maps with atom keys
                  variables =
                    if Map.has_key?(set, :variables) do
                      Enum.map(set[:variables], fn var ->
                        for {key, val} <- var, into: %{}, do: {String.to_atom(key), val}
                      end)
                    else
                      []
                    end

                  # Convert connections list items to maps with atom keys
                  connections =
                    if Map.has_key?(set, :connections) do
                      Enum.map(set[:connections], fn conn ->
                        # Convert connection string keys to atoms
                        conn = for {key, val} <- conn, into: %{}, do: {String.to_atom(key), val}

                        # Process topics if they exist
                        topics =
                          if Map.has_key?(conn, :topics) && is_list(conn.topics) do
                            Enum.map(conn.topics, fn topic ->
                              case topic do
                                # If topic is a map, convert its string keys to atoms
                                topic_map when is_map(topic_map) ->
                                  for {key, val} <- topic_map,
                                      into: %{},
                                      do: {String.to_atom(key), val}

                                # If topic is a string (old format), keep it as is
                                topic_str when is_binary(topic_str) ->
                                  topic_str

                                # For any other format, return as is
                                _ ->
                                  topic
                              end
                            end)
                          else
                            []
                          end

                        # Process scheduled_messages if they exist
                        scheduled_messages =
                          if Map.has_key?(conn, :scheduled_messages) &&
                               is_list(conn.scheduled_messages) do
                            Enum.map(conn.scheduled_messages, fn scheduled_msg ->
                              case scheduled_msg do
                                # If scheduled_msg is a map, convert its string keys to atoms
                                scheduled_msg_map when is_map(scheduled_msg_map) ->
                                  for {key, val} <- scheduled_msg_map,
                                      into: %{},
                                      do: {String.to_atom(key), val}

                                # For any other format, return as is
                                _ ->
                                  scheduled_msg
                              end
                            end)
                          else
                            []
                          end

                        # Process user_properties if they exist
                        user_properties =
                          if Map.has_key?(conn, :user_properties) &&
                               is_list(conn.user_properties) do
                            Enum.map(conn.user_properties, fn user_prop ->
                              case user_prop do
                                # If user_prop is a map, convert its string keys to atoms
                                user_prop_map when is_map(user_prop_map) ->
                                  for {key, val} <- user_prop_map,
                                      into: %{},
                                      do: {String.to_atom(key), val}

                                # For any other format, return as is
                                _ ->
                                  user_prop
                              end
                            end)
                          else
                            []
                          end

                        # Update the connection with the processed topics, scheduled_messages, and user_properties
                        conn
                        |> Map.put(:topics, topics)
                        |> Map.put(:scheduled_messages, scheduled_messages)
                        |> Map.put(:user_properties, user_properties)
                      end)
                    else
                      []
                    end

                  # Update the set with the converted variables and connections
                  set
                  |> Map.put(:variables, variables)
                  |> Map.put(:connections, connections)
                end)

              # Connection sets loaded successfully
              {:ok, connection_sets}

            {:error, reason} ->
              {:error, "Failed to decode connection sets JSON: #{inspect(reason)}"}
          end

        {:error, reason} ->
          {:error, "Failed to read connection sets file: #{reason}"}
      end
    else
      # If file doesn't exist, return an empty list
      {:ok, []}
    end
  end

  # Private function to load UI state
  defp load_ui_state do
    if File.exists?(@ui_state_file) do
      case File.read(@ui_state_file) do
        {:ok, json} ->
          case Jason.decode(json, keys: :atoms) do
            {:ok, ui_state} ->
              # Convert string keys to atoms for nested maps
              ui_state = deep_atomize_keys(ui_state)

              # Normalize broker name keys to strings for consistency across all UI state fields
              normalized_ui_state =
                ui_state
                |> normalize_broker_name_keys(:expanded_sets)
                |> normalize_broker_name_keys(:send_modal_forms)
                |> normalize_broker_name_keys(:mqtt5_properties_collapsed)
                |> normalize_broker_name_keys(:trace_filters)

              {:ok, normalized_ui_state}

            {:error, reason} ->
              {:error, "Failed to decode UI state JSON: #{inspect(reason)}"}
          end

        {:error, reason} ->
          {:error, "Failed to read UI state file: #{reason}"}
      end
    else
      # If file doesn't exist, return an empty map
      {:ok, %{expanded_sets: %{}}}
    end
  end

  # Helper function to normalize broker name keys to strings for UI state fields
  defp normalize_broker_name_keys(ui_state, field_key) do
    field_data = Map.get(ui_state, field_key, %{})

    # Only normalize if the field contains a map (broker name -> data structure)
    normalized_field_data =
      if is_map(field_data) do
        field_data
        |> Enum.map(fn {key, value} -> {to_string(key), value} end)
        |> Enum.into(%{})
      else
        field_data
      end

    Map.put(ui_state, field_key, normalized_field_data)
  end

  # Helper function to convert string keys to atoms in nested maps
  defp deep_atomize_keys(map) when is_map(map) do
    Map.new(map, fn
      # Special handling for connection sets to ensure name is preserved
      {:name, value} -> {:name, value}
      {"name", value} -> {:name, value}
      {key, value} when is_map(value) -> {key, deep_atomize_keys(value)}
      {key, value} when is_list(value) -> {key, Enum.map(value, &deep_atomize_value/1)}
      {key, value} -> {key, value}
    end)
  end

  defp deep_atomize_keys(list) when is_list(list) do
    Enum.map(list, &deep_atomize_value/1)
  end

  defp deep_atomize_value(value) when is_map(value) do
    # Special handling for maps that might be connection sets
    if Map.has_key?(value, :name) || Map.has_key?(value, "name") do
      # Preserve the name field
      name = Map.get(value, :name) || Map.get(value, "name")
      value = deep_atomize_keys(value)
      Map.put(value, :name, name)
    else
      deep_atomize_keys(value)
    end
  end

  defp deep_atomize_value(value) when is_list(value), do: Enum.map(value, &deep_atomize_value/1)
  defp deep_atomize_value(value), do: value
end
