defmodule Mqttable.MqttTelemetryHandler do
  @moduledoc """
  Telemetry handler for MQTT events.
  Handles telemetry events emitted by the EMQTT client library and processes them
  for MQTT packet tracing and monitoring.
  """

  require Logger

  @handler_id "emqtt-telemetry-handler"

  @doc """
  Start the telemetry handler by attaching to EMQTT telemetry events.
  """
  def start_handler(_opts \\ []) do
    case :telemetry.attach_many(
           @handler_id,
           [
             [:emqtt, :websocket, :recv_data],
             [:emqtt, :socket, :recv_data],
             [:emqtt, :socket, :send_data],
             [:emqtt, :socket, :send_data_failed]
           ],
           &__MODULE__.handle_event/4,
           []
         ) do
      :ok ->
        :ok

      {:error, :already_exists} ->
        Logger.info("EMQTT Telemetry Handler already exists")
        :ok

      {:error, reason} ->
        Logger.error("Failed to start EMQTT Telemetry Handler: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  Stop the telemetry handler by detaching from EMQTT telemetry events.
  """
  def stop_handler do
    case :telemetry.detach(@handler_id) do
      :ok ->
        Logger.info("EMQTT Telemetry Handler stopped")
        :ok

      {:error, :not_found} ->
        Logger.info("EMQTT Telemetry Handler was not running")
        :ok

      {:error, reason} ->
        Logger.error("Failed to stop EMQTT Telemetry Handler: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  Check if the telemetry handler is running.
  """
  def handler_running? do
    handlers = :telemetry.list_handlers([])
    Enum.any?(handlers, fn %{id: id} -> id == @handler_id end)
  end

  @doc """
  Handle telemetry events from EMQTT.
  """
  def handle_event(event_name, measurements, metadata, _config) do
    try do
      process_telemetry_event(event_name, measurements, metadata)
    catch
      type, reason ->
        stacktrace = __STACKTRACE__
        Logger.error("Error in telemetry handler: #{type} #{inspect(reason)}")
        Logger.error("Stacktrace:\n#{Exception.format_stacktrace(stacktrace)}")
        :ok
    end
  end

  # Process different telemetry events and convert them to report format
  defp process_telemetry_event([:emqtt, :websocket, :recv_data], measurements, metadata)
       when is_map(measurements) and is_map(metadata) do
    # Validate required data exists
    case Map.get(metadata, :data) do
      nil ->
        Logger.warning("Received websocket recv_data event with no data")
        :ok

      data ->
        report = %{
          msg: ~c"websocket_recv_data",
          data: data,
          clientid: Map.get(metadata, :client_id, "unknown"),
          data_size: Map.get(measurements, :data_size, 0)
        }

        Mqttable.MqttPacketProcessor.process_emqtt_report(report)
    end
  end

  defp process_telemetry_event([:emqtt, :socket, :recv_data], measurements, metadata)
       when is_map(measurements) and is_map(metadata) do
    # Validate required data exists
    case Map.get(metadata, :data) do
      nil ->
        Logger.warning("Received socket recv_data event with no data")
        :ok

      data ->
        report = %{
          msg: ~c"recv_data",
          data: data,
          clientid: Map.get(metadata, :client_id, "unknown"),
          data_size: Map.get(measurements, :data_size, 0)
        }

        Mqttable.MqttPacketProcessor.process_emqtt_report(report)
    end
  end

  defp process_telemetry_event([:emqtt, :socket, :send_data], measurements, metadata)
       when is_map(measurements) and is_map(metadata) do
    # Validate required packet data exists
    case Map.get(metadata, :data) do
      nil ->
        Logger.warning("Received socket send_data event with no packet data")
        :ok

      packet ->
        report = %{
          msg: ~c"send_data",
          packet: packet,
          clientid: Map.get(metadata, :client_id, "unknown"),
          data_size: Map.get(measurements, :data_size, 0)
        }

        Mqttable.MqttPacketProcessor.process_emqtt_report(report)
    end
  end

  defp process_telemetry_event([:emqtt, :socket, :send_data_failed], measurements, metadata)
       when is_map(measurements) and is_map(metadata) do
    # For send_data_failed, packet data might be nil, but reason should exist
    report = %{
      msg: ~c"send_data_failed",
      reason: Map.get(metadata, :reason, :unknown),
      packet: Map.get(metadata, :data),
      clientid: Map.get(metadata, :client_id, "unknown"),
      data_size: Map.get(measurements, :data_size, 0)
    }

    Mqttable.MqttPacketProcessor.process_emqtt_report(report)
  end

  # Handle invalid measurements or metadata
  defp process_telemetry_event(event_name, measurements, metadata)
       when not is_map(measurements) or not is_map(metadata) do
    Logger.warning(
      "Received telemetry event #{inspect(event_name)} with invalid measurements or metadata"
    )

    :ok
  end

  # Catch-all for unhandled events
  defp process_telemetry_event(event_name, _measurements, _metadata) do
    Logger.debug("Unhandled telemetry event: #{inspect(event_name)}")
    :ok
  end
end
